# Get the source of the commit message (e.g., 'message', 'template', 'merge', 'squash', 'commit')
# and the commit hash if it's a rebase/merge/squash
commit_msg_source=$2 # In prepare-commit-msg, $2 is the commit message source

# Only run Commitizen if the commit message source is empty or 'message' (i.e., not a merge, revert, squash, etc.)
# This prevents Commitizen from interfering with automated commit messages.
if [ -z "$commit_msg_source" ] || [ "$commit_msg_source" = "message" ]; then
  exec < /dev/tty && npx git-cz --hook || true
fi