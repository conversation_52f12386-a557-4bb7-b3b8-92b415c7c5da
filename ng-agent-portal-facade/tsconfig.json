{
  "compilerOptions": {
    "target": "es2018",
    "module": "commonjs",
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "baseUrl": ".",
    "paths": {
      "@controllers/*": ["src/api/controllers/*"],
      "@middlewares/*": ["src/api/middlewares/*"],
      "@dtos/*": ["src/api/dtos/*"],
      "@routes/*": ["src/api/routes/*"],
      "@services/*": ["src/api/services/*"],
      "@config/*": ["src/config/*"],
      "@db/*": ["src/db/*"],
      "@types/*": ["src/types/*"],
      "@utils/*": ["src/utils/*"]
    }
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"],
  
}
