/**
 * Utility functions for generating Swagger documentation comments
 */

/**
 * Interface for endpoint parameters
 */
export interface SwaggerParameter {
  name: string;
  in: 'path' | 'query' | 'header' | 'cookie';
  description: string;
  required?: boolean;
  schema: {
    type: 'string' | 'number' | 'integer' | 'boolean' | 'array' | 'object';
    format?: string;
    enum?: string[];
    default?: any;
    items?: any;
  };
  example?: any;
}

/**
 * Interface for endpoint response
 */
export interface SwaggerResponse {
  code: number;
  description: string;
  schema?: string;
  isArray?: boolean;
}

/**
 * Interface for endpoint request body
 */
export interface SwaggerRequestBody {
  description: string;
  required: boolean;
  schema: string;
}

/**
 * Interface for endpoint options
 */
export interface SwaggerEndpointOptions {
  summary: string;
  description: string;
  tags: string[];
  parameters?: SwaggerParameter[];
  requestBody?: SwaggerRequestBody;
  responses: SwaggerResponse[];
  security?: boolean;
}

/**
 * Generate Swagger comment for an endpoint
 * @param method HTTP method (get, post, put, delete, etc.)
 * @param path Endpoint path
 * @param options Endpoint options
 * @returns Swagger comment as a string
 */
export function generateSwaggerComment(
  method: 'get' | 'post' | 'put' | 'delete' | 'patch',
  path: string,
  options: SwaggerEndpointOptions,
): string {
  let comment = `/**\n * @swagger\n * ${path}:\n *   ${method}:\n`;

  // Add summary and description
  comment += ` *     summary: ${options.summary}\n`;
  comment += ` *     description: ${options.description}\n`;

  // Add tags
  comment += ` *     tags: [${options.tags.join(', ')}]\n`;

  // Add security if needed
  if (options.security) {
    comment += ` *     security:\n *       - bearerAuth: []\n`;
  }

  // Add parameters if any
  if (options.parameters && options.parameters.length > 0) {
    comment += ` *     parameters:\n`;
    options.parameters.forEach(param => {
      comment += ` *       - in: ${param.in}\n`;
      comment += ` *         name: ${param.name}\n`;
      if (param.required) {
        comment += ` *         required: true\n`;
      }
      comment += ` *         schema:\n`;

      // Handle schema
      if (param.schema.type === 'array') {
        comment += ` *           type: array\n`;
        if (param.schema.items) {
          comment += ` *           items:\n`;
          comment += ` *             type: ${param.schema.items.type}\n`;
        }
      } else if (param.schema.enum) {
        comment += ` *           type: ${param.schema.type}\n`;
        comment += ` *           enum: [${param.schema.enum.join(', ')}]\n`;
        if (param.schema.default) {
          comment += ` *           default: ${param.schema.default}\n`;
        }
      } else {
        comment += ` *           type: ${param.schema.type}\n`;
        if (param.schema.format) {
          comment += ` *           format: ${param.schema.format}\n`;
        }
      }

      comment += ` *         description: ${param.description}\n`;

      // Add example if provided
      if (param.example !== undefined) {
        comment += ` *         example: ${JSON.stringify(param.example)}\n`;
      }
    });
  }

  // Add request body if any
  if (options.requestBody) {
    comment += ` *     requestBody:\n`;
    comment += ` *       required: ${options.requestBody.required}\n`;
    comment += ` *       content:\n`;
    comment += ` *         application/json:\n`;
    comment += ` *           schema:\n`;
    comment += ` *             $ref: '${options.requestBody.schema}'\n`;
  }

  // Add responses
  comment += ` *     responses:\n`;
  options.responses.forEach(response => {
    comment += ` *       ${response.code}:\n`;
    comment += ` *         description: ${response.description}\n`;

    // Add schema if provided
    if (response.schema) {
      comment += ` *         content:\n`;
      comment += ` *           application/json:\n`;
      comment += ` *             schema:\n`;

      if (response.schema.startsWith('#')) {
        // Reference to a schema
        if (response.isArray) {
          comment += ` *               type: array\n`;
          comment += ` *               items:\n`;
          comment += ` *                 $ref: '${response.schema}'\n`;
        } else {
          comment += ` *               $ref: '${response.schema}'\n`;
        }
      } else {
        // Custom schema
        comment += ` *               type: object\n`;
        comment += ` *               properties:\n`;
        comment += ` *                 success:\n`;
        comment += ` *                   type: boolean\n`;
        comment += ` *                   example: ${
          response.code < 400 ? 'true' : 'false'
        }\n`;
        comment += ` *                 message:\n`;
        comment += ` *                   type: string\n`;
        comment += ` *                   example: ${response.description}\n`;

        if (response.schema === 'data') {
          comment += ` *                 data:\n`;
          if (response.isArray) {
            comment += ` *                   type: array\n`;
            comment += ` *                   items:\n`;
            comment += ` *                     type: object\n`;
          } else {
            comment += ` *                   type: object\n`;
          }
        } else if (response.schema === 'error') {
          comment += ` *                 errors:\n`;
          comment += ` *                   type: object\n`;
          comment += ` *                   additionalProperties:\n`;
          comment += ` *                     type: array\n`;
          comment += ` *                     items:\n`;
          comment += ` *                       type: string\n`;
        }
      }
    }
  });

  comment += ` */`;

  return comment;
}

/**
 * Generate Swagger comment for a schema
 * @param name Schema name
 * @param description Schema description
 * @param properties Schema properties
 * @param required Required properties
 * @param example Example object
 * @returns Swagger comment as a string
 */
export function generateSwaggerSchemaComment(
  name: string,
  description: string,
  properties: Record<
    string,
    {
      type: string;
      description: string;
      format?: string;
      enum?: string[];
      items?: any;
      ref?: string;
    }
  >,
  required: string[] = [],
  example: Record<string, any> = {},
): string {
  let comment = `/**\n * @swagger\n * components:\n *   schemas:\n *     ${name}:\n`;

  comment += ` *       type: object\n`;

  if (description) {
    comment += ` *       description: ${description}\n`;
  }

  if (required.length > 0) {
    comment += ` *       required:\n`;
    required.forEach(prop => {
      comment += ` *         - ${prop}\n`;
    });
  }

  comment += ` *       properties:\n`;

  // Add properties
  Object.entries(properties).forEach(([propName, propDetails]) => {
    comment += ` *         ${propName}:\n`;

    if (propDetails.ref) {
      // Reference to another schema
      comment += ` *           $ref: '${propDetails.ref}'\n`;
    } else if (propDetails.type === 'array') {
      comment += ` *           type: array\n`;
      comment += ` *           items:\n`;

      if (propDetails.items && propDetails.items.ref) {
        comment += ` *             $ref: '${propDetails.items.ref}'\n`;
      } else if (propDetails.items) {
        comment += ` *             type: ${propDetails.items.type}\n`;
      }
    } else {
      comment += ` *           type: ${propDetails.type}\n`;

      if (propDetails.format) {
        comment += ` *           format: ${propDetails.format}\n`;
      }

      if (propDetails.enum) {
        comment += ` *           enum: [${propDetails.enum.join(', ')}]\n`;
      }
    }

    comment += ` *           description: ${propDetails.description}\n`;
  });

  // Add example if provided
  if (Object.keys(example).length > 0) {
    comment += ` *       example:\n`;
    Object.entries(example).forEach(([key, value]) => {
      comment += ` *         ${key}: ${JSON.stringify(value)}\n`;
    });
  }

  comment += ` */`;

  return comment;
}
