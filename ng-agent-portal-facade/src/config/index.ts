import dotenv from "dotenv";

// Load environment variables from .env file
dotenv.config();

interface Config {
  env: string;
  port: number;
  host: string;
  apiPrefix: string;
  services: {
    agentPortal: {
      baseUrl: string;
    };
    botBuilder: {
      baseUrl: string;
    };
    botInteraction: {
      baseUrl: string;
    };
    chat: {
      baseUrl: string;
    };
  };
  jwt: {
    secret: string;
    expiresIn: string;
  };
  logging: {
    level: string;
  };
  rateLimit: {
    windowMs: number;
    max: number;
  };
}

const config: Config = {
  env: process.env.NODE_ENV ?? "development",
  port: parseInt(process.env.PORT ?? "4001", 10),
  host: process.env.HOST ?? "http://localhost",
  apiPrefix: process.env.API_PREFIX ?? "/api/v1",
  services: {
    agentPortal: {
      baseUrl:
        process.env.AGENT_PORTAL_SERVICE_URL ?? "http://localhost:3001/api/v1",
    },
    botBuilder: {
      baseUrl:
        process.env.BOT_BUILDER_SERVICE_URL ?? "http://localhost:3001/api/v1",
    },
    botInteraction: {
      baseUrl:
        process.env.BOT_INTERACTION_SERVICE_URL ??
        "http://localhost:3004/api/v1",
    },
    chat: {
      baseUrl: process.env.CHAT_SERVICE_URL ?? "http://localhost:3002/api/v1",
    },
  },
  jwt: {
    secret: process.env.JWT_SECRET ?? "your_jwt_secret",
    expiresIn: process.env.JWT_EXPIRES_IN ?? "1d",
  },
  logging: {
    level: process.env.LOG_LEVEL ?? "info",
  },
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS ?? "900000", 10), // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_MAX ?? "100", 10), // limit each IP to 100 requests per windowMs
  },
};

export default config;
