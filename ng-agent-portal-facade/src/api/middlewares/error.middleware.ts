import { Request, Response, NextFunction } from "express";
import axios, { AxiosError } from "axios";
import { AppError } from "@utils/errors";
import logger from "@utils/logger";
import { errorResponse } from "@utils/response";

export const errorHandler = (
  err: Error,
  req: Request,
  res: Response,
  _next: NextFunction
): void => {
  let statusCode = 500;
  let message = "Internal Server Error";
  let errors: Record<string, string[]> | undefined;

  // Handle known AppError
  if (err instanceof AppError) {
    statusCode = err.statusCode;
    message = err.message;
    if ("errors" in err && err.errors) {
      errors = err.errors as Record<string, string[]>;
    }
  }

  // Handle Axios errors
  else if (axios.isAxiosError(err)) {
    const axiosError = err as AxiosError;
    statusCode = axiosError.response?.status ?? 500;
    message = axiosError.message;

    logger.error(`[${req.id ?? "UNKNOWN"}] Axios Error: ${message}`, {
      path: `${req.method} ${req.path}`,
      axiosUrl: axiosError.config?.url,
      statusCode,
      error: axiosError.stack,
      axiosResponse: axiosError.response?.data,
    });

    res.status(statusCode).json({
      ...(typeof axiosError.response?.data === "object" &&
      axiosError.response.data !== null
        ? (axiosError.response.data as Record<string, string[]>)
        : {}),
    });
    return;
  }

  // Log other errors
  logger.error(`[${req.id ?? "UNKNOWN"}] Error: ${message}`, {
    path: `${req.method} ${req.path}`,
    error: err.stack,
    statusCode,
  });

  // Send standard error response
  errorResponse(res, statusCode, errors);
};
