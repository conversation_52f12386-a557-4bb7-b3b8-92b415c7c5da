import { Request, Response, NextFunction } from "express";
import { JwksClient } from "jwks-rsa";
import jwt from "jsonwebtoken";
import logger from "@utils/logger";
import { UnauthorizedError } from "@utils/errors";

// Extend Express Request interface
declare global {
  namespace Express {
    interface Request {
      user?: any;
      token?: string;
    }
  }
}

const issuer =
  process.env.KEYCLOAK_ISSUER ??
  "http://keycloak.3rdparty.svc.cluster.local:8080/realms/master";

const client = new JwksClient({
  jwksUri: `${issuer}/protocol/openid-connect/certs`,
});

async function getSigningKey(kid: string): Promise<string> {
  const key = await client.getSigningKey(kid);
  return key.getPublicKey();
}

export const authenticate = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    if (
      process.env.NODE_ENV === "development" ||
      process.env.NODE_ENV === "production" // Need to remove this later
    ) {
      return next();
    } // Skip authentication in development mode

    const authHeader = req.headers.authorization;

    if (!authHeader?.startsWith("Bearer ")) {
      return res
        .status(401)
        .json({ error: "Missing or invalid Authorization header" });
    }

    const token = authHeader.split(" ")[1];
    req.token = token;

    const decoded = jwt.decode(token, { complete: true }) as {
      header: { kid: string };
      payload: any;
    } | null;

    if (!decoded?.header?.kid) {
      throw new UnauthorizedError("Invalid token structure");
    }

    const key = await getSigningKey(decoded.header.kid);

    const payload = jwt.verify(token, key, {
      algorithms: ["RS256"],
      issuer,
    }) as any;

    req.user = {
      ...payload,
      id: payload.sub,
    };

    next();
  } catch (error: any) {
    logger.error("Authentication failed:", error.message);
    res.status(401).json({ error: "Unauthorized" });
  }
};
