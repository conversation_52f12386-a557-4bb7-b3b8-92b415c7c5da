import { BaseService } from "./base.service";
import config from "@config/index";

/**
 * Service for department operations
 * Extends BaseService to handle department-specific operations
 */
class DepartmentService extends BaseService {
  /**
   * Create a new DepartmentService
   */
  constructor() {
    super(config.services.agentPortal.baseUrl, "departments");
  }

  /**
   * Get default department
   * @param token - JWT token for authentication
   */
  async getDefaultDepartment(token: string) {
    return this.request(
      {
        method: "GET",
        url: `${this.endpoint}/default`,
      },
      token
    );
  }

  // Add any department-specific methods here
}

export default new DepartmentService();
