import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from "axios";
import logger from "@utils/logger";
import { ServiceUnavailableError } from "@utils/errors";

/**
 * Base service for REST API connections
 * Acts as a generic connector to microservices
 */
export class BaseService {
  protected client: AxiosInstance;
  protected endpoint: string;

  /**
   * Create a new BaseService
   * @param baseURL - Base URL for the service
   * @param endpoint - API endpoint for this resource
   */
  constructor(baseURL: string, endpoint: string) {
    this.client = axios.create({
      baseURL,
      timeout: 10000,
    });
    this.endpoint = endpoint;
  }

  /**
   * Get all resources
   * @param token - JWT token for authentication
   * @param queryParams - Query parameters for filtering
   */
  async getAll(token: string, queryParams?: any): Promise<AxiosResponse> {
    try {
      return await this.client.get(this.endpoint, {
        headers: this.getAuthHeader(token),
        params: queryParams,
      });
    } catch (error) {
      logger.error(`Error fetching ${this.endpoint}:`, error);
      throw new ServiceUnavailableError(
        `Unable to fetch ${this.endpoint} from service`
      );
    }
  }

  /**
   * Get resource by ID
   * @param id - Resource ID
   * @param token - JWT token for authentication
   */
  async getById(id: string, token: string): Promise<AxiosResponse> {
    try {
      return await this.client.get(`${this.endpoint}/${id}`, {
        headers: this.getAuthHeader(token),
      });
    } catch (error) {
      logger.error(`Error fetching ${this.endpoint}/${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a new resource
   * @param data - Resource data
   * @param token - JWT token for authentication
   */
  async create(data: any, token: string): Promise<AxiosResponse> {
    try {
      return await this.client.post(this.endpoint, data, {
        headers: this.getAuthHeader(token),
      });
    } catch (error) {
      logger.error(`Error creating ${this.endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Update a resource
   * @param id - Resource ID
   * @param data - Resource data to update
   * @param token - JWT token for authentication
   */
  async update(id: string, data: any, token: string): Promise<AxiosResponse> {
    try {
      return await this.client.put(`${this.endpoint}/${id}`, data, {
        headers: this.getAuthHeader(token),
      });
    } catch (error) {
      logger.error(`Error updating ${this.endpoint}/${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a resource
   * @param id - Resource ID
   * @param token - JWT token for authentication
   */
  async delete(id: string, token: string): Promise<AxiosResponse> {
    try {
      return await this.client.delete(`${this.endpoint}/${id}`, {
        headers: this.getAuthHeader(token),
      });
    } catch (error) {
      logger.error(`Error deleting ${this.endpoint}/${id}:`, error);
      throw error;
    }
  }

  /**
   * Custom request to the service
   * @param config - Axios request config
   * @param token - JWT token for authentication
   */
  async request(
    config: AxiosRequestConfig,
    token: string
  ): Promise<AxiosResponse> {
    try {
      return await this.client.request({
        ...config,
        headers: {
          ...config.headers,
          ...this.getAuthHeader(token),
        },
      });
    } catch (error) {
      logger.error(`Error in custom request to ${config.url}:`, error);
      throw error;
    }
  }

  /**
   * Get authorization header with token
   * @param token - JWT token
   * @returns Authorization header object
   */
  protected getAuthHeader(token: string): Record<string, string> {
    return {
      Authorization: `Bearer ${token}`,
    };
  }
}
