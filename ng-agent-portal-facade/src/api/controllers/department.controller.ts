import { Request, Response, NextFunction } from "express";
import departmentService from "@services/department.service";
import { successResponse } from "@utils/response";

/**
 * Controller for department operations
 */
class DepartmentController {
  /**
   * Get all departments
   */
  async findAll(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const token = req.headers.authorization?.split(" ")[1] ?? "";
      const response = await departmentService.getAll(token, req.query);
      successResponse(res, response.data);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get department by ID
   */
  async findById(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const token = req.headers.authorization?.split(" ")[1] ?? "";
      const response = await departmentService.getById(req.params.id, token);
      successResponse(res, response.data);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create a new department
   */
  async create(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const token = req.headers.authorization?.split(" ")[1] ?? "";
      const response = await departmentService.create(req.body, token);
      successResponse(res, response.data, 201);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update a department
   */
  async update(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const token = req.headers.authorization?.split(" ")[1] ?? "";
      const response = await departmentService.update(
        req.params.id,
        req.body,
        token
      );
      successResponse(res, response.data);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete a department
   */
  async delete(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const token = req.headers.authorization?.split(" ")[1] ?? "";
      await departmentService.delete(req.params.id, token);
      successResponse(res, null);
    } catch (error) {
      next(error);
    }
  }
}

export default new DepartmentController();
