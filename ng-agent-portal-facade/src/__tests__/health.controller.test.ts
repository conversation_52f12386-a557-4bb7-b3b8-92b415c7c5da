import { Request, Response, NextFunction } from "express";
import healthController from "@controllers/health.controller";

describe("HealthController", () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let nextFunction: NextFunction;

  beforeEach(() => {
    mockRequest = {};
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    };
    nextFunction = jest.fn();
  });

  describe("check", () => {
    it("should return 200 status with health information", async () => {
      // Mock Date.now and process.uptime
      const originalDateNow = Date.now;
      const originalUptime = process.uptime;
      const mockDate = new Date("2023-01-01T00:00:00.000Z");

      global.Date = class extends Date {
        constructor() {
          super();
          return mockDate;
        }

        static now() {
          return mockDate.getTime();
        }
      } as any;

      process.uptime = jest.fn().mockReturnValue(123.456);

      // Call controller method
      await healthController.check(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction
      );

      // Restore original functions
      global.Date = originalDateNow as any;
      process.uptime = originalUptime;

      // Assertions
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        status: "ok",
        uptime: 123.456,
        timestamp: mockDate.toISOString(),
      });
      expect(nextFunction).not.toHaveBeenCalled();
    });

    it("should call next function with error if exception occurs", async () => {
      // Mock response.status to throw an error
      mockResponse.status = jest.fn().mockImplementation(() => {
        throw new Error("Test error");
      });

      // Call controller method
      await healthController.check(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction
      );

      // Assertions
      expect(nextFunction).toHaveBeenCalledWith(expect.any(Error));
      expect(mockResponse.json).not.toHaveBeenCalled();
    });
  });
});
