import { validate } from "class-validator";
import { CreateDepartmentDto, UpdateDepartmentDto } from "@dtos/department.dto";

describe("Department DTOs", () => {
  describe("CreateDepartmentDto", () => {
    it("should validate a valid DTO", async () => {
      const dto = new CreateDepartmentDto();
      dto.name = "Test Department";

      const errors = await validate(dto);
      expect(errors.length).toBe(0);
    });

    it("should fail validation when name is missing", async () => {
      const dto = new CreateDepartmentDto();
      // name is missing

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe("name");
    });

    it("should fail validation when name is empty", async () => {
      const dto = new CreateDepartmentDto();
      dto.name = "";

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe("name");
    });

    it("should fail validation when name is too long", async () => {
      const dto = new CreateDepartmentDto();
      dto.name = "a".repeat(101); // Max length is 100

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe("name");
    });
  });

  describe("UpdateDepartmentDto", () => {
    it("should validate a valid DTO", async () => {
      const dto = new UpdateDepartmentDto();
      dto.name = "Updated Department";

      const errors = await validate(dto);
      expect(errors.length).toBe(0);
    });

    it("should fail validation when name is empty", async () => {
      const dto = new UpdateDepartmentDto();
      dto.name = "";

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe("name");
    });

    it("should fail validation when name is too long", async () => {
      const dto = new UpdateDepartmentDto();
      dto.name = "a".repeat(101); // Max length is 100

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe("name");
    });

    it("should validate when name is missing (optional field)", async () => {
      const dto = new UpdateDepartmentDto();
      // name is missing, but it's optional for updates

      const errors = await validate(dto);
      expect(errors.length).toBe(0);
    });

    it("should validate a valid DTO with all fields", async () => {
      const dto = new UpdateDepartmentDto();
      dto.name = "Updated Department";

      const errors = await validate(dto);
      expect(errors.length).toBe(0);
    });
  });
});
