import departmentService from "@services/department.service";
import { ConflictError, NotFoundError } from "@utils/errors";
import { v4 as uuidv4 } from "uuid";

// Mock the client in the department service
jest.mock("axios");

describe("DepartmentService", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock the client.request method
    (departmentService as any).client = {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      patch: jest.fn(),
      delete: jest.fn(),
    };
  });

  describe("create", () => {
    it("should create a department successfully", async () => {
      // Mock data
      const departmentData = { name: "Test Department", isDefault: false };
      const createdDepartment = { ...departmentData, id: uuidv4() };
      const token = "test-token";

      // Mock client response
      (departmentService as any).client.post.mockResolvedValue({
        data: createdDepartment,
      });

      // Call the service method
      const result = await departmentService.create(departmentData, token);

      // Assertions
      expect((departmentService as any).client.post).toHaveBeenCalledWith(
        "departments",
        departmentData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      console.log(result);
      expect(result.data).toEqual(createdDepartment);
    });
  });

  describe("findAll", () => {
    it("should return all departments", async () => {
      // Mock data
      const departments = [
        { id: uuidv4(), name: "Department 1", isDefault: true },
        { id: uuidv4(), name: "Department 2", isDefault: false },
      ];
      const options = { limit: 10, offset: 0 };
      const token = "test-token";

      // Mock client response
      (departmentService as any).client.get.mockResolvedValue({
        data: departments,
      });

      // Call the service method
      const result = await departmentService.getAll(token, options);

      // Assertions
      expect((departmentService as any).client.get).toHaveBeenCalledWith(
        "departments",
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
          params: options,
        }
      );
      expect(result.data).toEqual(departments);
    });
  });

  describe("findById", () => {
    it("should return a department by ID", async () => {
      // Mock data
      const departmentId = uuidv4();
      const department = {
        id: departmentId,
        name: "Test Department",
        isDefault: false,
      };
      const token = "test-token";

      // Mock client response
      (departmentService as any).client.get.mockResolvedValue({
        data: department,
      });

      // Call the service method
      const result = await departmentService.getById(departmentId, token);

      // Assertions
      expect((departmentService as any).client.get).toHaveBeenCalledWith(
        `departments/${departmentId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      expect(result.data).toEqual(department);
    });
  });

  describe("update", () => {
    it("should update a department successfully", async () => {
      // Mock data
      const departmentId = uuidv4();
      const updateData = { name: "Updated Department" };
      const updatedDepartment = {
        id: departmentId,
        name: "Updated Department",
        isDefault: false,
      };
      const token = "test-token";

      // Mock client response
      (departmentService as any).client.put.mockResolvedValue({
        data: updatedDepartment,
      });

      // Call the service method
      const result = await departmentService.update(
        departmentId,
        updateData,
        token
      );

      // Assertions
      expect((departmentService as any).client.put).toHaveBeenCalledWith(
        `departments/${departmentId}`,
        updateData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      expect(result.data).toEqual(updatedDepartment);
    });
  });

  describe("delete", () => {
    it("should delete a department successfully", async () => {
      // Mock data
      const departmentId = uuidv4();
      const token = "test-token";

      // Mock client response
      (departmentService as any).client.delete.mockResolvedValue({
        status: 204,
      });

      // Call the service method
      await departmentService.delete(departmentId, token);

      // Assertions
      expect((departmentService as any).client.delete).toHaveBeenCalledWith(
        `departments/${departmentId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
    });
  });
});
