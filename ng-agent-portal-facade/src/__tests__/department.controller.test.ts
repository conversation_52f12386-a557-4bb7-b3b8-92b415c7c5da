import { Request, Response } from "express";
import departmentController from "@controllers/department.controller";
import departmentService from "../api/services/department.service";
import { BadRequestError } from "@utils/errors";
import { v4 as uuidv4 } from "uuid";

// Mock the department service
jest.mock("../api/services/department.service");

describe("DepartmentController", () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let nextFunction: jest.Mock;

  beforeEach(() => {
    mockRequest = {
      headers: {
        authorization: "Bearer test-token",
      },
    };
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    nextFunction = jest.fn();
    jest.clearAllMocks();
  });

  describe("create", () => {
    it("should create a department and return 201 status", async () => {
      // Mock data
      const departmentData = { name: "Test Department" };
      const createdDepartment = {
        id: uuidv4(),
        ...departmentData,
        isDefault: false,
      };

      // Setup request
      mockRequest.body = departmentData;

      // Mock service response
      (departmentService.create as jest.Mock).mockResolvedValue(
        createdDepartment
      );

      // Call controller
      await departmentController.create(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction
      );

      // Assertions
      expect(departmentService.create).toHaveBeenCalled();
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalled();
      expect(nextFunction).not.toHaveBeenCalled();
    });

    it("should pass errors to next function", async () => {
      // Mock data
      const departmentData = { name: "Test Department" };
      const error = new BadRequestError("Service error");

      // Setup request
      mockRequest.body = departmentData;

      // Mock service to throw error
      (departmentService.create as jest.Mock).mockRejectedValue(error);

      // Call controller
      await departmentController.create(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction
      );

      // Assertions
      expect(departmentService.create).toHaveBeenCalled();
      expect(nextFunction).toHaveBeenCalledWith(error);
    });
  });

  describe("findAll", () => {
    it("should return all departments with 200 status", async () => {
      // Mock data
      const departments = [
        { id: uuidv4(), name: "Department 1", isDefault: true },
        { id: uuidv4(), name: "Department 2", isDefault: false },
      ];

      // Setup request

      // Mock service response
      (departmentService.getAll as jest.Mock).mockResolvedValue(departments);

      // Call controller
      await departmentController.findAll(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction
      );

      // Assertions
      expect(departmentService.getAll).toHaveBeenCalled();
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalled();
      expect(nextFunction).not.toHaveBeenCalled();
    });

    it("should pass errors to next function", async () => {
      // Mock data
      const error = new BadRequestError("Service error");

      // Setup request

      // Mock service to throw error
      (departmentService.getAll as jest.Mock).mockRejectedValue(error);

      // Call controller
      await departmentController.findAll(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction
      );

      // Assertions
      expect(departmentService.getAll).toHaveBeenCalled();
      expect(nextFunction).toHaveBeenCalledWith(error);
    });
  });

  describe("findById", () => {
    it("should return a department by ID with 200 status", async () => {
      // Mock data
      const departmentId = uuidv4();
      const department = {
        id: departmentId,
        name: "Test Department",
        isDefault: false,
      };

      // Setup request
      mockRequest.params = { id: departmentId };

      // Mock service response
      (departmentService.getById as jest.Mock).mockResolvedValue(department);

      // Call controller
      await departmentController.findById(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction
      );

      // Assertions
      expect(departmentService.getById).toHaveBeenCalledWith(
        departmentId,
        "test-token"
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalled();
      expect(nextFunction).not.toHaveBeenCalled();
    });

    it("should pass errors to next function", async () => {
      // Mock data
      const departmentId = uuidv4();
      const error = new BadRequestError("Service error");

      // Setup request
      mockRequest.params = { id: departmentId };

      // Mock service to throw error
      (departmentService.getById as jest.Mock).mockRejectedValue(error);

      // Call controller
      await departmentController.findById(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction
      );

      // Assertions
      expect(departmentService.getById).toHaveBeenCalledWith(
        departmentId,
        "test-token"
      );
      expect(nextFunction).toHaveBeenCalledWith(error);
    });
  });

  describe("update", () => {
    it("should update a department and return 200 status", async () => {
      // Mock data
      const departmentId = uuidv4();
      const updateData = { name: "Updated Department" };
      const updatedDepartment = {
        id: departmentId,
        ...updateData,
        isDefault: false,
      };

      // Setup request
      mockRequest.params = { id: departmentId };
      mockRequest.body = updateData;

      // Mock service response
      (departmentService.update as jest.Mock).mockResolvedValue(
        updatedDepartment
      );

      // Call controller
      await departmentController.update(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction
      );

      // Assertions
      expect(departmentService.update).toHaveBeenCalled();
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalled();
      expect(nextFunction).not.toHaveBeenCalled();
    });
  });

  describe("delete", () => {
    it("should delete a department and return 204 status", async () => {
      // Mock data
      const departmentId = uuidv4();

      // Setup request
      mockRequest.params = { id: departmentId };

      // Mock service response
      (departmentService.delete as jest.Mock).mockResolvedValue(undefined);

      // Call controller
      await departmentController.delete(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction
      );

      // Assertions
      expect(nextFunction).not.toHaveBeenCalled();
    });
  });
});
