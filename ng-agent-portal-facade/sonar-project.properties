sonar.host.url=http://sonar.comviva.com/sonar
sonar.links.scm=http://blrgitlab.comviva.com/mbs/ng/sf/chatbot/ng-agent-portal-facade.git
sonar.projectVersion=8.0
sonar.sources=./src
sonar.branch.name=ng-mainline-dev
sonar.cfamily.build-wrapper-output.bypass=true
sonar.projectName=MCS_CPAAS_8x_RM_Agent-Portal-Facade
sonar.projectKey=my:MCS_CPAAS_8x_RM_Agent-Portal-Facade
sonar.exclusions=**/*.spec.ts, **/__tests__/**, **/test/**, **/mocks/**





