# nGage Agent Portal Facade Service

A facade service for the nGage Agent Portal, providing a unified interface to communicate with multiple underlying services.

## Features

- Service aggregation
- Unified interface for agent portal functionality
- Request routing and orchestration
- API gateway for agent portal microservices

## Tech Stack

- Node.js with Express
- TypeScript
- Swagger API documentation
- Class-validator for DTO validation
- Winston for logging

## Getting Started

### Prerequisites

- Node.js 16+

### Installation

1. Clone the repository
2. Install dependencies:

```bash
npm install
```

3. Create a `.env` file based on `.env.example`:

```bash
cp .env.example .env
```

4. Update the environment variables in the `.env` file

5. Start the development server:

```bash
npm run dev
```

The API will be available at http://localhost:4001/api/v1 and the Swagger documentation at http://localhost:4001/api-docs.

## API Endpoints

The facade service provides a unified API that routes requests to the appropriate microservices:

- Agent Portal Service
- Bot Builder Service
- Bot Interaction Service
- Chat Service

## Development

### Available Scripts

- `npm run dev` - Start the development server with hot-reload
- `npm run build` - Build the application
- `npm start` - Start the production server
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run format` - Format code with Prettier
- `npm test` - Run tests

## Docker

Build the Docker image:

```bash
docker build -t ng-agent-portal-facade .
```

Run the container:

```bash
docker run -p 4001:4001 --env-file .env ng-agent-portal-facade
```

## License

[MIT](LICENSE)
