<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Department API Test</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <h1>Department API Test</h1>
        
        <div class="card">
            <h2>Get All Departments</h2>
            <button id="getDepartments">Fetch Departments</button>
            <div id="departmentsResult" class="result"></div>
        </div>
        
        <div class="card">
            <h2>Create Department</h2>
            <form id="createDepartmentForm">
                <div class="form-group">
                    <label for="departmentName">Department Name:</label>
                    <input type="text" id="departmentName" required>
                </div>
                <div class="form-group">
                    <label for="isDefault">Is Default:</label>
                    <input type="checkbox" id="isDefault">
                </div>
                <button type="submit">Create</button>
            </form>
            <div id="createResult" class="result"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="app.js"></script>
</body>
</html>