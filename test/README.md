# Department API Test App

This is a simple frontend application to test the Department API and verify CORS configuration.

## How to Use

1. Open the `index.html` file in your browser or serve it using a simple HTTP server.

2. If your API is running on a different URL than `http://localhost:4001/api/v1`, update the `API_BASE_URL` variable in `app.js`.

3. Use the "Fetch Departments" button to test the GET endpoint.

4. Use the form to create a new department and test the POST endpoint.

## Running with a Simple HTTP Server

You can use any of these methods to serve the files:

### Using Python

```bash
# Python 3
python -m http.server 8080

# Python 2
python -m SimpleHTTPServer 8080
```

### Using Node.js

First, install `http-server`:

```bash
npm install -g http-server
```

Then run:

```bash
http-server -p 8080
```

Then open your browser to `http://localhost:8080`

## Troubleshooting CORS Issues

If you see CORS errors in the console:

1. Make sure your API server has CORS enabled
2. Check that the origin of this app is allowed in your CORS configuration
3. Verify that the API server includes the necessary CORS headers in its responses