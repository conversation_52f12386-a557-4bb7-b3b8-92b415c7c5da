// API base URL - change this to match your API endpoint
const API_BASE_URL = 'http://localhost:4001/api/v1';

// Function to display results or errors
function displayResult(elementId, data, isError = false) {
    const element = document.getElementById(elementId);
    
    if (isError) {
        element.innerHTML = `<div class="error">Error: ${JSON.stringify(data, null, 2)}</div>`;
    } else {
        element.innerHTML = `<div class="success">Success: ${JSON.stringify(data, null, 2)}</div>`;
    }
}

// Function to get all departments
async function getDepartments() {
    const resultElement = document.getElementById('departmentsResult');
    resultElement.innerHTML = 'Loading...';
    
    try {
        const response = await axios.get(`${API_BASE_URL}/departments`);
        displayResult('departmentsResult', response.data);
    } catch (error) {
        console.error('Error fetching departments:', error);
        
        // Display detailed error information
        const errorData = {
            message: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            headers: error.response?.headers
        };
        
        displayResult('departmentsResult', errorData, true);
    }
}

// Function to create a department
async function createDepartment(name, isDefault) {
    const resultElement = document.getElementById('createResult');
    resultElement.innerHTML = 'Creating...';
    
    try {
        const response = await axios.post(`${API_BASE_URL}/departments`, {
            name,
            isDefault
        });
        
        displayResult('createResult', response.data);
    } catch (error) {
        console.error('Error creating department:', error);
        
        // Display detailed error information
        const errorData = {
            message: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            headers: error.response?.headers
        };
        
        displayResult('createResult', errorData, true);
    }
}

// Event listeners
document.addEventListener('DOMContentLoaded', () => {
    // Get departments button
    document.getElementById('getDepartments').addEventListener('click', getDepartments);
    
    // Create department form
    document.getElementById('createDepartmentForm').addEventListener('submit', (e) => {
        e.preventDefault();
        
        const name = document.getElementById('departmentName').value;
        const isDefault = document.getElementById('isDefault').checked;
        
        createDepartment(name, isDefault);
    });
});

// Add a request interceptor to log all requests (helpful for debugging)
axios.interceptors.request.use(function (config) {
    console.log('Request:', config);
    return config;
}, function (error) {
    return Promise.reject(error);
});

// Add a response interceptor to log all responses (helpful for debugging)
axios.interceptors.response.use(function (response) {
    console.log('Response:', response);
    return response;
}, function (error) {
    console.log('Response Error:', error);
    return Promise.reject(error);
});