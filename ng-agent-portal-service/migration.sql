
-- Switch to the `account` database
USE `agent_portal`;

-- Create the `departments` table
CREATE TABLE IF NOT EXISTS `departments` (
  `id` VARCHAR(36) NOT NULL DEFAULT (UUID()),
  `name` VARCHAR(255) NOT NULL UNIQUE,
  `isDefault` BOOLEAN NOT NULL DEFAULT FALSE,
  `createdAt` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deletedAt` DATETIME DEFAULT NULL ON DELETE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- Create an index on the `name` column (optional, since it's already UNIQUE)
CREATE INDEX `departments_name_idx` ON `departments` (`name`);

-- Insert initial department records (optional seed data)
INSERT INTO `departments` (`id`, `name`, `isDefault`, `createdAt`, `updatedAt`)
VALUES
  (UUID(), 'Default', TRUE, NOW(), NOW());
 