sonar.host.url=http://sonar.comviva.com/sonar
sonar.links.scm=http://blrgitlab.comviva.com/mbs/ng/sf/chatbot/ng-agent-portal-service.git
sonar.projectVersion=8.0
sonar.sources=./src
sonar.cfamily.build-wrapper-output.bypass=true
sonar.projectName=MCS_CPAAS_8x_RM_Agent-Portal-Service
sonar.projectKey=my:MCS_CPAAS_8x_RM_Agent-Portal-Service
sonar.branch.name=ng-mainline-dev
sonar.exclusions=**/*.spec.ts, **/__tests__/**, **/test/**, **/mocks/**





