import {Sequelize} from 'sequelize';
import config from '@config/index';
import logger from '@utils/logger';

const sequelize = new Sequelize({
  dialect: config.db.dialect,
  host: config.db.host,
  port: config.db.port,
  database: config.db.name,
  username: config.db.user,
  password: config.db.password,
  logging: msg => logger.debug(msg),
  pool: {
    max: config.db.poolMax,
    min: config.db.poolMin,
    acquire: config.db.acquireTime,
    idle: config.db.idleTime,
  },
});

export const connectDatabase = async (): Promise<void> => {
  try {
    logger.info('env config:', config);
    await sequelize.authenticate();
    logger.info('Database connection has been established successfully.');
  } catch (error) {
    logger.error('Unable to connect to the database:', error);
    process.exit(1);
  }
};

export default sequelize;
