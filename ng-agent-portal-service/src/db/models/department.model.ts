import {Model, DataTypes, Optional} from 'sequelize';
import sequelize from '@db/index';

// Department attributes interface
export interface DepartmentAttributes {
  id: string;
  name: string;
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
}

// Department creation attributes interface (optional fields for creation)
export type DepartmentCreationAttributes = Optional<
  DepartmentAttributes,
  'id' | 'createdAt' | 'updatedAt' | 'deletedAt'
>;

// Department model class
class Department extends Model<
  DepartmentAttributes,
  DepartmentCreationAttributes
> {
  declare id: string;
  declare name: string;
  declare isDefault: boolean;
  declare createdAt: Date;
  declare updatedAt: Date;
  declare deletedAt?: Date;

  static associate(_models: any): void {
    Department.hasMany(_models.Agent, {
      foreignKey: 'departmentId',
      as: 'agents',
    });
  }
}

// Initialize Department model
Department.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
      field: 'id',
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      field: 'name',
    },
    isDefault: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      field: 'is_default',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at',
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at',
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'deleted_at',
    },
  },
  {
    sequelize,
    modelName: 'Department',
    tableName: 'departments',
    timestamps: true,
    paranoid: true,
    underscored: true,
  },
);

export default Department;
