import fs from 'fs';
import path from 'path';
import {Sequelize} from 'sequelize';
import sequelize from '@db/index';

interface ModelRegistry {
  [key: string]: any;
}

const models: ModelRegistry = {};

// Read all model files in the current directory
fs.readdirSync(__dirname)
  .filter(file => {
    return (
      file.indexOf('.') !== 0 && file !== 'index.ts' && file.slice(-3) === '.ts'
    );
  })
  .forEach(file => {
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const model = require(path.join(__dirname, file)).default;
    models[model.name] = model;
  });

// Set up associations
Object.keys(models).forEach(modelName => {
  if (models[modelName].associate) {
    models[modelName].associate(models);
  }
});

export {sequelize, Sequelize};
export default models;
