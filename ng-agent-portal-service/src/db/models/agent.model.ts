import {Model, DataTypes, Optional} from 'sequelize';
import sequelize from '@db/index';

export interface AgentAttributes {
  id: string;
  name: string;
  email: string;
  mobile: string;
  departmentId: string | null;
  availability: string;
  status: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
}

export type AgentCreationAttributes = Optional<
  AgentAttributes,
  'id' | 'createdAt' | 'updatedAt' | 'deletedAt'
>;

class Agent extends Model<AgentAttributes, AgentCreationAttributes> {
  declare id: string;
  declare name?: string;
  declare email?: string;
  declare mobile?: string;
  declare departmentId: string | null;
  declare availability: string;
  declare status: string;
  declare createdAt: Date;
  declare updatedAt: Date;
  declare deletedAt?: Date;

  static associate(_models: any): void {
    Agent.belongsTo(_models.Department, {
      foreignKey: 'departmentId',
      as: 'department',
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });
  }
}

Agent.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
      field: 'id',
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'name',
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'email',
    },
    mobile: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'mobile',
    },
    departmentId: {
      type: DataTypes.UUID,
      allowNull: true,
      field: 'department_id',
    },
    availability: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'offline',
      validate: {
        isIn: [['offline', 'online', 'away', 'busy']],
      },
      field: 'availability',
    },
    status: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'inactive',
      field: 'status',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at',
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at',
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'deleted_at',
    },
  },
  {
    sequelize,
    modelName: 'Agent',
    tableName: 'agents',
    timestamps: true,
    paranoid: true,
    underscored: true,
  },
);

export default Agent;
