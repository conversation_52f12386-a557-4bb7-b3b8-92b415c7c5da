import {Transaction} from 'sequelize';
import {BaseRepository} from './base.repository';
import Department from '@db/models/department.model';

/**
 * Repository for Department model
 */
export class DepartmentRepository extends BaseRepository<Department> {
  /**
   * Create a new DepartmentRepository instance
   */
  constructor() {
    super(Department);
  }

  /**
   * Get default department
   * @param transaction - Optional transaction
   * @returns Promise with default department or null
   */
  async getDefaultDepartment(
    transaction?: Transaction,
  ): Promise<Department | null> {
    return this.findOne({
      where: {isDefault: true},
      transaction,
    });
  }
}

// Export singleton instance
export default new DepartmentRepository();
