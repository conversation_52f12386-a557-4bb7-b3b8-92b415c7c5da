'use strict';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const {v4: uuidv4} = require('uuid');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Check if default department already exists
    const departments = await queryInterface.sequelize.query(
      'SELECT * FROM departments WHERE is_default = true',
      {type: Sequelize.QueryTypes.SELECT},
    );

    // Only create default department if none exists
    if (departments.length === 0) {
      const now = new Date();
      await queryInterface.bulkInsert('departments', [
        {
          id: uuidv4(),
          name: 'Default',
          is_default: true,
          created_at: now,
          updated_at: now,
        },
      ]);
    }
  },

  async down(queryInterface) {
    // Remove the default department
    await queryInterface.bulkDelete('departments', {
      name: 'Default',
      isDefault: true,
    });
  },
};
