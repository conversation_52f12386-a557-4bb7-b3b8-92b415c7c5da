import dotenv from 'dotenv';
import {Dialect} from 'sequelize';

// Load environment variables from .env file
dotenv.config();

interface Config {
  env: string;
  port: number;
  apiPrefix: string;
  host: string;
  db: {
    host: string;
    port: number;
    name: string;
    user: string;
    password: string;
    dialect: Dialect;
    poolMin: number;
    poolMax: number;
    acquireTime: number;
    idleTime: number;
  };
  jwt: {
    secret: string;
    expiresIn: string;
  };
  logging: {
    level: string;
  };
  rateLimit: {
    windowMs: number;
    max: number;
  };
}

const config: Config = {
  env: process.env.NODE_ENV ?? 'development',
  port: parseInt(process.env.PORT ?? '3001', 10),
  host: process.env.HOST ?? 'http://localhost',
  apiPrefix: process.env.API_PREFIX ?? '/api/v1',
  db: {
    host: process.env.DB_HOST ?? 'localhost',
    port: parseInt(process.env.DB_PORT ?? '3306', 10),
    name: process.env.DB_DATABASE ?? 'ng_bot_builder_db',
    user: process.env.DB_USER ?? 'root',
    password: process.env.DB_PASSWORD ?? '',
    dialect: (process.env.DB_DIALECT ?? 'mysql') as Dialect,
    poolMin: parseInt(process.env.DB_POOL_MIN ?? '0', 10),
    poolMax: parseInt(process.env.DB_POOL_MAX ?? '5', 10),
    idleTime: parseInt(process.env.DB_IDLE_TIME ?? '10000', 10),
    acquireTime: parseInt(process.env.DB_ACQUIRE_TIME ?? '30000', 10),
  },
  jwt: {
    secret: process.env.JWT_SECRET ?? 'your_jwt_secret',
    expiresIn: process.env.JWT_EXPIRES_IN ?? '1d',
  },
  logging: {
    level: process.env.LOG_LEVEL ?? 'info',
  },
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS ?? '900000', 10), // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_MAX ?? '100', 10), // limit each IP to 100 requests per windowMs
  },
};

export default config;
