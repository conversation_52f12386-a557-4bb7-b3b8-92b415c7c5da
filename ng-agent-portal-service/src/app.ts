import express, {Application, Request, Response, NextFunction} from 'express';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import swaggerUi from 'swagger-ui-express';
import 'reflect-metadata';

import config from '@config/index';
import swaggerSpec from '@config/swagger';
import {requestId, requestLogger, errorHandler} from '@middlewares/index';
import {NotFoundError} from '@utils/errors';
import logger from '@utils/logger';

// Import routes
import healthRoutes from '@routes/health.routes';
import departmentRoutes from '@routes/department.routes';
import agentRoutes from '@routes/agent.routes';

class App {
  public app: Application;

  constructor() {
    this.app = express();
    this.configureMiddleware();
    this.configureRoutes();
    this.configureErrorHandling();
  }

  private configureMiddleware(): void {
    // Security middleware
    this.app.use(helmet());

    // Request parsing
    this.app.use(express.json());
    this.app.use(express.urlencoded({extended: true}));
    this.app.use(cors());

    // Request logging
    this.app.use(requestId);
    this.app.use(requestLogger);

    // Rate limiting
    const limiter = rateLimit({
      windowMs: config.rateLimit.windowMs,
      max: config.rateLimit.max,
      standardHeaders: true,
      legacyHeaders: false,
    });
    this.app.use(limiter);

    // Swagger documentation
    this.app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));
    this.app.get('/swagger.json', (_req, res) => {
      res.setHeader('Content-Type', 'application/json');
      res.send(swaggerSpec);
    });
  }

  private configureRoutes(): void {
    // API routes
    this.app.use(`${config.apiPrefix}/health`, healthRoutes);
    this.app.use(`${config.apiPrefix}/departments`, departmentRoutes);
    this.app.use(`${config.apiPrefix}/agents`, agentRoutes);

    // Handle 404 errors
    this.app.use((req: Request, _res: Response, next: NextFunction) => {
      logger.warn(`Route not found: ${req.method} ${req.url}`);
      next(new NotFoundError(`Route not found: ${req.method} ${req.url}`));
    });
  }

  private configureErrorHandling(): void {
    this.app.use(errorHandler);
  }
}

export default new App().app;
