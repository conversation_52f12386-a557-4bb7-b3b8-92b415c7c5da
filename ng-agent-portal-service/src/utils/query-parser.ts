import {Request} from 'express';
import {
  FindOptions,
  Op,
  WhereOptions,
  Includeable,
  ModelStatic,
  Model,
} from 'sequelize';

/**
 * Interface for filter options
 */
export interface FilterOptions {
  limit?: number;
  offset?: number;
  where?: Record<string, any>;
  include?: string[];
  order?: [string, 'ASC' | 'DESC'][];
}

/**
 * Configuration for query parsing
 */
export interface QueryParserConfig<T extends Model> {
  defaultLimit?: number;
  maxLimit?: number;
  allowedFilters?: (keyof T)[];
  allowedIncludes?: Record<string, ModelStatic<any>>;
  allowedSortFields?: (keyof T)[];
}

/**
 * Parse query parameters from request into Sequelize FindOptions
 * @param req - Express request object
 * @param config - Query parser configuration
 * @returns Sequelize FindOptions
 */
export function parseQueryParams<T extends Model>(
  req: Request,
  config: QueryParserConfig<T>,
): FindOptions {
  const {
    defaultLimit = 10,
    maxLimit = 100,
    allowedFilters = [],
    allowedIncludes = {},
    allowedSortFields = [],
  } = config;

  const options: FindOptions = {};
  const query = req.query;

  // Handle pagination
  if (query.limit) {
    const limit = parseInt(query.limit as string, 10);
    options.limit = Math.min(isNaN(limit) ? defaultLimit : limit, maxLimit);
  } else {
    options.limit = defaultLimit;
  }

  if (query.offset) {
    const offset = parseInt(query.offset as string, 10);
    options.offset = isNaN(offset) ? 0 : offset;
  }

  // Handle sorting
  if (query.sort) {
    const sortField = query.sort as string;
    const sortOrder = query.order === 'desc' ? 'DESC' : 'ASC';

    if (allowedSortFields.includes(sortField as any)) {
      options.order = [[sortField, sortOrder]];
    }
  }

  // Handle filtering
  if (query.filter) {
    try {
      const filters =
        typeof query.filter === 'string'
          ? JSON.parse(query.filter)
          : query.filter;

      const whereClause: WhereOptions = {};

      // Process each filter
      Object.keys(filters).forEach(key => {
        if (allowedFilters.includes(key as any)) {
          const value = filters[key];

          // Handle different filter types
          if (typeof value === 'object' && value !== null) {
            // Handle operators like gt, lt, eq, etc.
            const operators: Record<symbol, any> = {};

            Object.keys(value).forEach(op => {
              switch (op) {
                case 'eq':
                  operators[Op.eq] = value[op];
                  break;
                case 'ne':
                  operators[Op.ne] = value[op];
                  break;
                case 'gt':
                  operators[Op.gt] = value[op];
                  break;
                case 'gte':
                  operators[Op.gte] = value[op];
                  break;
                case 'lt':
                  operators[Op.lt] = value[op];
                  break;
                case 'lte':
                  operators[Op.lte] = value[op];
                  break;
                case 'like':
                  operators[Op.like] = `%${value[op]}%`;
                  break;
                case 'startsWith':
                  operators[Op.startsWith] = value[op];
                  break;
                case 'endsWith':
                  operators[Op.endsWith] = value[op];
                  break;
                case 'in':
                  operators[Op.in] = Array.isArray(value[op])
                    ? value[op]
                    : [value[op]];
                  break;
                case 'notIn':
                  operators[Op.notIn] = Array.isArray(value[op])
                    ? value[op]
                    : [value[op]];
                  break;
                case 'between':
                  if (Array.isArray(value[op]) && value[op].length === 2) {
                    operators[Op.between] = value[op];
                  }
                  break;
              }
            });

            whereClause[key] = operators;
          } else {
            // Simple equality
            whereClause[key] = value;
          }
        }
      });

      if (Object.keys(whereClause).length > 0) {
        options.where = whereClause;
      }
    } catch (error) {
      // If JSON parsing fails, ignore the filter
      console.error('Error parsing filter:', error);
    }
  }

  // Handle includes
  if (query.include) {
    try {
      const includes = (query.include as string).split(',');
      const validIncludes: Includeable[] = [];

      includes.forEach(include => {
        if (include in allowedIncludes) {
          validIncludes.push({model: allowedIncludes[include]});
        }
      });

      if (validIncludes.length > 0) {
        options.include = validIncludes;
      }
    } catch (error) {
      console.error('Error parsing include:', error);
    }
  }

  return options;
}
