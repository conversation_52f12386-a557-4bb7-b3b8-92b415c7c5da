import {Request, Response} from 'express';
import departmentController from '@controllers/department.controller';
import departmentService from '@services/department.service';
import {HttpError} from '@utils/errors';
import {v4 as uuidv4} from 'uuid';

// Mock the department service
jest.mock('@services/department.service', () => ({
  create: jest.fn(),
  findAll: jest.fn(),
  findById: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  count: jest.fn(),
  getDefaultDepartment: jest.fn(),
}));

describe('DepartmentController', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let nextFunction: jest.Mock;

  beforeEach(() => {
    mockRequest = {};
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    nextFunction = jest.fn();
    jest.clearAllMocks();
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  describe('create', () => {
    it('should create a department and return 201 status', async () => {
      // Mock data
      const departmentData = {name: 'Test Department'};
      const createdDepartment = {
        id: uuidv4(),
        ...departmentData,
        isDefault: false,
      };

      // Setup request
      mockRequest.body = departmentData;

      // Mock service response
      (departmentService.create as jest.Mock).mockResolvedValue(
        createdDepartment,
      );

      // Call controller
      await departmentController.create(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction,
      );

      // Assertions
      expect(departmentService.create).toHaveBeenCalledWith(departmentData);
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: createdDepartment,
        message: 'Department created successfully',
      });
      expect(nextFunction).not.toHaveBeenCalled();
    });

    it('should call next with error if service throws', async () => {
      // Mock data
      const departmentData = {name: 'Test Department'};
      const error = new Error('Service error');

      // Setup request
      mockRequest.body = departmentData;

      // Mock service to throw error
      (departmentService.create as jest.Mock).mockRejectedValue(error);

      // Call controller
      await departmentController.create(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction,
      );

      // Assertions
      expect(departmentService.create).toHaveBeenCalledWith(departmentData);
      expect(mockResponse.status).not.toHaveBeenCalled();
      expect(mockResponse.json).not.toHaveBeenCalled();
      expect(nextFunction).toHaveBeenCalledWith(error);
    });
  });

  describe('findAll', () => {
    it('should return all departments with 200 status', async () => {
      // Mock data
      const departments = [
        {id: uuidv4(), name: 'Department 1', isDefault: true},
        {id: uuidv4(), name: 'Department 2', isDefault: false},
      ];
      const total = departments.length;
      const options = {limit: 10, offset: 0};

      // Setup request
      mockRequest.filterOptions = options;
      mockRequest.token = 'test-token';

      // Mock service response
      (departmentService.findAll as jest.Mock).mockResolvedValue(departments);
      (departmentService.count as jest.Mock).mockResolvedValue(total);

      // Call controller
      await departmentController.findAll(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction,
      );

      // Assertions
      expect(departmentService.findAll).toHaveBeenCalledWith(
        options,
        'test-token',
      );
      expect(departmentService.count).toHaveBeenCalledWith(options);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: departments,
        message: 'Departments retrieved successfully',
        meta: {
          total,
          limit: options.limit,
          offset: options.offset,
        },
      });
      expect(nextFunction).not.toHaveBeenCalled();
    });

    it('should call next with error if service throws', async () => {
      // Mock service to throw error
      const error = new Error('Service error');
      (departmentService.findAll as jest.Mock).mockRejectedValue(error);

      // Setup request
      mockRequest.filterOptions = {};

      // Call controller
      await departmentController.findAll(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction,
      );

      // Assertions
      expect(departmentService.findAll).toHaveBeenCalled();
      expect(mockResponse.status).not.toHaveBeenCalled();
      expect(mockResponse.json).not.toHaveBeenCalled();
      expect(nextFunction).toHaveBeenCalledWith(error);
    });
  });

  describe('findById', () => {
    it('should return a department by ID with 200 status', async () => {
      // Mock data
      const departmentId = uuidv4();
      const department = {
        id: departmentId,
        name: 'Test Department',
        isDefault: false,
      };
      const options = {};

      // Setup request
      mockRequest.params = {id: departmentId};
      mockRequest.filterOptions = options;
      mockRequest.token = 'test-token';

      // Mock service response
      (departmentService.findById as jest.Mock).mockResolvedValue(department);

      // Call controller
      await departmentController.findById(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction,
      );

      // Assertions
      expect(departmentService.findById).toHaveBeenCalledWith(
        departmentId,
        options,
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: department,
        message: 'Department retrieved successfully',
      });
      expect(nextFunction).not.toHaveBeenCalled();
    });

    it('should call next with error if service throws', async () => {
      // Mock data
      const departmentId = uuidv4();
      const error = new HttpError(404, 'Department not found');

      // Setup request
      mockRequest.params = {id: departmentId};
      mockRequest.filterOptions = {};

      // Mock service to throw error
      (departmentService.findById as jest.Mock).mockRejectedValue(error);

      // Call controller
      await departmentController.findById(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction,
      );

      // Assertions
      expect(departmentService.findById).toHaveBeenCalledWith(departmentId, {});
      expect(mockResponse.status).not.toHaveBeenCalled();
      expect(mockResponse.json).not.toHaveBeenCalled();
      expect(nextFunction).toHaveBeenCalledWith(error);
    });
  });

  describe('update', () => {
    it('should update a department and return 200 status', async () => {
      // Mock data
      const departmentId = uuidv4();
      const updateData = {name: 'Updated Department'};
      const updatedDepartment = {
        id: departmentId,
        name: 'Updated Department',
        isDefault: false,
      };

      // Setup request
      mockRequest.params = {id: departmentId};
      mockRequest.body = updateData;

      // Mock service response
      (departmentService.update as jest.Mock).mockResolvedValue(
        updatedDepartment,
      );

      // Call controller
      await departmentController.update(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction,
      );

      // Assertions
      expect(departmentService.update).toHaveBeenCalledWith(
        departmentId,
        updateData,
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: updatedDepartment,
        message: 'Department updated successfully',
      });
      expect(nextFunction).not.toHaveBeenCalled();
    });

    it('should call next with error if service throws', async () => {
      // Mock data
      const departmentId = uuidv4();
      const updateData = {name: 'Updated Department'};
      const error = new HttpError(404, 'Department not found');

      // Setup request
      mockRequest.params = {id: departmentId};
      mockRequest.body = updateData;

      // Mock service to throw error
      (departmentService.update as jest.Mock).mockRejectedValue(error);

      // Call controller
      await departmentController.update(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction,
      );

      // Assertions
      expect(departmentService.update).toHaveBeenCalledWith(
        departmentId,
        updateData,
      );
      expect(mockResponse.status).not.toHaveBeenCalled();
      expect(mockResponse.json).not.toHaveBeenCalled();
      expect(nextFunction).toHaveBeenCalledWith(error);
    });
  });

  describe('delete', () => {
    it('should delete a department and return 204 status', async () => {
      // Mock data
      const departmentId = uuidv4();

      // Setup request
      mockRequest.params = {id: departmentId};

      // Mock service response
      (departmentService.delete as jest.Mock).mockResolvedValue(undefined);

      // Call controller
      await departmentController.delete(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction,
      );

      // Assertions
      expect(departmentService.delete).toHaveBeenCalledWith(departmentId);
      expect(mockResponse.status).toHaveBeenCalledWith(204);
      expect(mockResponse.json).not.toHaveBeenCalled(); // 204 No Content doesn't return a body
    });

    it('should call next with error if service throws', async () => {
      // Mock data
      const departmentId = uuidv4();
      const error = new HttpError(404, 'Department not found');

      // Setup request
      mockRequest.params = {id: departmentId};

      // Mock service to throw error
      (departmentService.delete as jest.Mock).mockRejectedValue(error);

      // Call controller
      await departmentController.delete(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction,
      );

      // Assertions
      expect(departmentService.delete).toHaveBeenCalledWith(departmentId);
      expect(mockResponse.status).not.toHaveBeenCalled();
      expect(mockResponse.json).not.toHaveBeenCalled();
      expect(nextFunction).toHaveBeenCalledWith(error);
    });
  });
});
