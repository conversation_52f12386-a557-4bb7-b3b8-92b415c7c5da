import request from 'supertest';
import express, {Request, Response, NextFunction} from 'express';
import departmentRoutes from '@routes/department.routes';
import departmentController from '@controllers/department.controller';
import {validateDto} from '@middlewares/validation.middleware';
import {authenticate} from '@middlewares/authenticate.middleware';
import {v4 as uuidv4} from 'uuid';

const departmentId = uuidv4();

// Mock middleware and controller
jest.mock('@middlewares/validation.middleware', () => ({
  validateDto: jest.fn(
    _schema => (_req: Request, _res: Response, next: NextFunction) => next(),
  ),
}));

jest.mock('@middlewares/authenticate.middleware', () => ({
  authenticate: jest.fn((req, res, next) => {
    req.user = {id: 1, email: '<EMAIL>'};
    next();
  }),
}));

jest.mock('@controllers/department.controller', () => ({
  create: jest.fn((req, res) =>
    res.status(201).json({id: departmentId, name: 'Test Department'}),
  ),
  findAll: jest.fn((req, res) =>
    res.status(200).json([{id: departmentId, name: 'Test Department'}]),
  ),
  findById: jest.fn((req, res) =>
    res.status(200).json({id: departmentId, name: 'Test Department'}),
  ),
  update: jest.fn((req, res) =>
    res.status(200).json({id: departmentId, name: 'Updated Department'}),
  ),
  delete: jest.fn((req, res) => res.status(204).json()),
}));

describe('Department Routes', () => {
  let app: express.Application;

  beforeEach(() => {
    app = express();
    app.use(express.json());
    app.use('/api/v1/departments', departmentRoutes);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/v1/departments', () => {
    it('should create a new department', async () => {
      const response = await request(app)
        .post('/api/v1/departments')
        .send({name: 'Test Department1'});
      expect(response.status).toBe(201);
      expect(response.body).toEqual({
        id: departmentId,
        name: 'Test Department',
      });
      expect(authenticate).toHaveBeenCalled();
      expect(validateDto).toHaveBeenCalled();
      expect(departmentController.create).toHaveBeenCalled();
    });
  });

  describe('GET /api/v1/departments', () => {
    it('should return all departments', async () => {
      const response = await request(app).get('/api/v1/departments');

      expect(response.status).toBe(200);
      expect(response.body).toEqual([
        {id: departmentId, name: 'Test Department'},
      ]);
      expect(authenticate).toHaveBeenCalled();
      expect(departmentController.findAll).toHaveBeenCalled();
    });
  });

  describe('GET /api/v1/departments/:id', () => {
    it('should return a department by ID', async () => {
      const response = await request(app).get('/api/v1/departments/1');

      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        id: departmentId,
        name: 'Test Department',
      });
      expect(authenticate).toHaveBeenCalled();
      expect(departmentController.findById).toHaveBeenCalled();
    });
  });

  describe('PATCH /api/v1/departments/:id', () => {
    it('should update a department', async () => {
      const response = await request(app)
        .patch(`/api/v1/departments/${departmentId}`)
        .send({name: 'Updated Department'});

      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        id: departmentId,
        name: 'Updated Department',
      });
      expect(authenticate).toHaveBeenCalled();
      expect(departmentController.update).toHaveBeenCalled();
    });
  });

  describe('DELETE /api/v1/departments/:id', () => {
    it('should delete a department', async () => {
      const response = await request(app).delete(
        `/api/v1/departments/${departmentId}`,
      );

      expect(response.status).toBe(204);
      expect(authenticate).toHaveBeenCalled();
      expect(departmentController.delete).toHaveBeenCalled();
    });
  });
});
