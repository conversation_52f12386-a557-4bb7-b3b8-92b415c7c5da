import {DepartmentAttributes} from '@db/models/department.model';
import departmentService from '@services/department.service';
import departmentRepository from '@db/repositories/department.repository';
import {ConflictError, HttpError} from '@utils/errors';
import {v4 as uuidv4} from 'uuid';

const id = uuidv4();

// Mock the department repository
jest.mock('@db/repositories/department.repository', () => {
  return {
    findOne: jest.fn(),
    findAll: jest.fn(),
    findById: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    getDefaultDepartment: jest.fn(),
  };
});

describe('DepartmentService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    it('should create a department successfully', async () => {
      // Mock data
      const departmentData = {name: 'Test Department', isDefault: false};
      const createdDepartment = {...departmentData, id};

      // Mock repository.findOne to return null (no existing department)
      (departmentRepository.findOne as jest.Mock).mockResolvedValue(null);

      // Mock repository.create to return the created department
      (departmentRepository.create as jest.Mock).mockResolvedValue(
        createdDepartment,
      );

      // Call the service method
      const result = await departmentService.create(
        departmentData as DepartmentAttributes,
      );

      // Assertions
      expect(departmentRepository.findOne).toHaveBeenCalledWith({
        where: {name: departmentData.name},
        paranoid: false,
      });
      expect(departmentRepository.create).toHaveBeenCalledWith(departmentData, {
        transaction: undefined,
      });
      expect(result).toEqual(createdDepartment);
    });

    it('should throw ConflictError if department with same name exists', async () => {
      // Mock data
      const departmentData = {name: 'Existing Department', isDefault: false};
      const existingDepartment = {...departmentData, id};

      // Mock repository.findOne to return an existing department
      (departmentRepository.findOne as jest.Mock).mockResolvedValue(
        existingDepartment,
      );

      // Call the service method and expect it to throw
      await expect(
        departmentService.create(departmentData as DepartmentAttributes),
      ).rejects.toThrow(ConflictError);

      // Assertions
      expect(departmentRepository.findOne).toHaveBeenCalledWith({
        where: {name: departmentData.name},
        paranoid: false,
      });
      expect(departmentRepository.create).not.toHaveBeenCalled();
    });
  });

  describe('findAll', () => {
    it('should return all departments', async () => {
      // Mock data
      const departments = [
        {id: uuidv4(), name: 'Department 1', isDefault: true},
        {id: uuidv4(), name: 'Department 2', isDefault: false},
      ];

      // Mock repository.findAll to return departments
      (departmentRepository.findAll as jest.Mock).mockResolvedValue(
        departments,
      );

      // Call the service method
      const result = await departmentService.findAll();

      // Assertions
      expect(departmentRepository.findAll).toHaveBeenCalled();
      expect(result).toEqual(departments);
    });
  });

  describe('findById', () => {
    it('should return a department by ID', async () => {
      // Mock data
      const department = {
        id,
        name: 'Test Department',
        isDefault: false,
      };

      // Mock repository.findById to return the department
      (departmentRepository.findById as jest.Mock).mockResolvedValue(
        department,
      );

      // Call the service method
      const result = await departmentService.findById(id);

      // Assertions
      expect(departmentRepository.findById).toHaveBeenCalledWith(id, undefined);
      expect(result).toEqual(department);
    });

    it('should throw HttpError if department not found', async () => {
      // Mock repository.findById to return null
      (departmentRepository.findById as jest.Mock).mockResolvedValue(null);
      const departmentId = uuidv4();

      // Call the service method and expect it to throw
      await expect(departmentService.findById(departmentId)).rejects.toThrow(
        new HttpError(404, 'Department not found'),
      );

      // Assertions
      expect(departmentRepository.findById).toHaveBeenCalledWith(
        departmentId,
        undefined,
      );
    });
  });

  describe('update', () => {
    it('should update a department successfully', async () => {
      // Mock data
      const departmentId = uuidv4();
      const updateData = {name: 'Updated Department'};
      const department = {
        id: departmentId,
        name: 'Original Department',
        isDefault: false,
      };
      const updatedDepartment = {
        ...department,
        name: updateData.name,
      };

      // Mock repository.findById to return the department
      (departmentRepository.findById as jest.Mock).mockResolvedValue(
        department,
      );

      // Mock repository.findOne to return null (no conflict)
      (departmentRepository.findOne as jest.Mock).mockResolvedValue(null);

      // Mock repository.update to return the updated department
      (departmentRepository.update as jest.Mock).mockResolvedValue(
        updatedDepartment,
      );

      // Call the service method
      const result = await departmentService.update(departmentId, updateData);

      // Assertions
      expect(departmentRepository.findById).toHaveBeenCalledWith(departmentId, {
        transaction: undefined,
      });
      expect(departmentRepository.findOne).toHaveBeenCalledWith({
        where: {name: updateData.name},
        paranoid: false,
        transaction: undefined,
      });
      expect(departmentRepository.update).toHaveBeenCalledWith(
        departmentId,
        updateData,
        {transaction: undefined},
      );
      expect(result).toEqual(updatedDepartment);
    });

    it('should throw HttpError if department not found', async () => {
      const departmentId = uuidv4();
      // Mock repository.findById to return null
      (departmentRepository.findById as jest.Mock).mockResolvedValue(null);

      // Call the service method and expect it to throw
      await expect(
        departmentService.update(departmentId, {name: 'New Name'}),
      ).rejects.toThrow(new HttpError(404, 'Department not found'));

      // Assertions
      expect(departmentRepository.findById).toHaveBeenCalledWith(departmentId, {
        transaction: undefined,
      });
    });

    it('should throw HttpError if updated name already exists', async () => {
      // Mock data
      const departmentId = uuidv4();
      const updateData = {name: 'Existing Department'};
      const department = {
        id: departmentId,
        name: 'Original Department',
        isDefault: false,
      };
      const existingDepartment = {id: uuidv4(), name: 'Existing Department'};

      // Mock repository.findById to return the department
      (departmentRepository.findById as jest.Mock).mockResolvedValue(
        department,
      );

      // Mock repository.findOne to return an existing department
      (departmentRepository.findOne as jest.Mock).mockResolvedValue(
        existingDepartment,
      );

      // Call the service method and expect it to throw
      await expect(
        departmentService.update(departmentId, updateData),
      ).rejects.toThrow(
        new HttpError(409, 'Department with this name already exists'),
      );

      // Assertions
      expect(departmentRepository.findById).toHaveBeenCalledWith(departmentId, {
        transaction: undefined,
      });
      expect(departmentRepository.findOne).toHaveBeenCalledWith({
        where: {name: updateData.name},
        paranoid: false,
        transaction: undefined,
      });
      expect(departmentRepository.update).not.toHaveBeenCalled();
    });
  });

  describe('delete', () => {
    it('should delete a department successfully', async () => {
      // Mock data
      const departmentId = uuidv4();
      const department = {
        id: departmentId,
        name: 'Test Department',
        isDefault: false,
      };

      // Mock repository.findById to return the department
      (departmentRepository.findById as jest.Mock).mockResolvedValue(
        department,
      );

      // Mock repository.delete to return undefined
      (departmentRepository.delete as jest.Mock).mockResolvedValue(undefined);

      // Call the service method
      await departmentService.delete(departmentId);

      // Assertions
      expect(departmentRepository.findById).toHaveBeenCalledWith(departmentId, {
        transaction: undefined,
      });
      expect(departmentRepository.delete).toHaveBeenCalledWith(departmentId, {
        transaction: undefined,
      });
    });

    it('should throw HttpError if department not found', async () => {
      const departmentId = uuidv4();
      // Mock repository.findById to return null
      (departmentRepository.findById as jest.Mock).mockResolvedValue(null);

      // Call the service method and expect it to throw
      await expect(departmentService.delete(departmentId)).rejects.toThrow(
        new HttpError(404, 'Department not found'),
      );

      // Assertions
      expect(departmentRepository.findById).toHaveBeenCalledWith(departmentId, {
        transaction: undefined,
      });
      expect(departmentRepository.delete).not.toHaveBeenCalled();
    });

    it('should throw HttpError if trying to delete default department', async () => {
      // Mock data
      const departmentId = uuidv4();
      const department = {
        id: departmentId,
        name: 'Default Department',
        isDefault: true,
      };

      // Mock repository.findById to return the department
      (departmentRepository.findById as jest.Mock).mockResolvedValue(
        department,
      );

      // Call the service method and expect it to throw
      await expect(departmentService.delete(departmentId)).rejects.toThrow(
        new HttpError(400, 'Cannot delete the default department'),
      );

      // Assertions
      expect(departmentRepository.findById).toHaveBeenCalledWith(departmentId, {
        transaction: undefined,
      });
      expect(departmentRepository.delete).not.toHaveBeenCalled();
    });
  });

  describe('getDefaultDepartment', () => {
    it('should return the default department', async () => {
      // Mock data
      const defaultDepartment = {
        id: uuidv4(),
        name: 'Default Department',
        isDefault: true,
      };

      // Mock repository.findOne to return the default department
      (
        departmentRepository.getDefaultDepartment as jest.Mock
      ).mockResolvedValue(defaultDepartment);

      // Call the service method
      const result = await departmentService.getDefaultDepartment();

      // Assertions
      expect(result).toEqual(defaultDepartment);
    });
  });
});
