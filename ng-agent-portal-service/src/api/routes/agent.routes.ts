import {Router} from 'express';
import agent<PERSON>ontroller from '@controllers/agent.controller';
import {authenticate} from '@middlewares/index';
import {validateDto} from '@middlewares/validation.middleware';
import {CreateAgentDto, UpdateAgentDto} from '../dtos';
import {parseQueryFilter} from '@middlewares/params.middleware';
import {applyFilters} from '@middlewares/filter.middleware';
import Agent from '@db/models/agent.model';
const router = Router();

/**
 * @swagger
 * tags:
 *   name: Agents
 *   description: Agent management
 */

router.use(authenticate);

/**
 * @swagger
 * /agents:
 *   post:
 *     summary: Create a new agent
 *     tags: [Agents]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateAgentDto'
 *     responses:
 *       201:
 *         description: Agent created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AgentResponseDto'
 *       400:
 *         description: Validation error
 *     security:
 *       - bearerAuth: []
 */
router.post('/', validateDto(CreateAgentDto), agentController.create);

/**
 * @swagger
 * /agents:
 *   get:
 *     summary: Get all agents
 *     tags: [Agents]
 *     responses:
 *       200:
 *         description: A list of agents
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/AgentResponseDto'
 *     security:
 *       - bearerAuth: []
 */
router.get(
  '/',
  applyFilters(Agent, {
    allowedAttributes: [
      'id',
      'name',
      'email',
      'mobile',
      'status',
      'availability',
      'departmentId',
    ],
    maxLimit: 100,
    allowedOperators: [], // All operators are allowed
  }),
  parseQueryFilter,
  agentController.findAll,
);

/**
 * @swagger
 * /agents/{id}:
 *   get:
 *     summary: Get agent
 *     tags: [Agents]
 *     responses:
 *       200:
 *         description: An agents
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/AgentResponseDto'
 *     security:
 *       - bearerAuth: []
 */
router.get('/:id', agentController.findById);

/**
 * @swagger
 * /agents/{id}:
 *   put:
 *     summary: Update an agent
 *     tags: [Agents]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Agent ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateAgentDto'
 *     responses:
 *       200:
 *         description: Agent updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AgentResponseDto'
 *       404:
 *         description: Agent not found
 *     security:
 *       - bearerAuth: []
 */
router.put('/:id', validateDto(UpdateAgentDto), agentController.update);

/**
 * @swagger
 * /agents/{id}:
 *   delete:
 *     summary: Delete an agent
 *     tags: [Agents]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Agent ID
 *     responses:
 *       204:
 *         description: Agent deleted successfully
 *       404:
 *         description: Agent not found
 *     security:
 *       - bearerAuth: []
 */
router.delete('/:id', agentController.delete);

/**
 * @swagger
 * /agents/{id}/activate:
 *   put:
 *     summary: Activates an agent
 *     tags: [Agents]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Agent ID
 *     responses:
 *       200:
 *         description: Agent activated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AgentResponseDto'
 *       404:
 *         description: Agent not found
 *     security:
 *       - bearerAuth: []
 */
router.put('/:id/activate', agentController.activate);

/**
 * @swagger
 * /agents/{id}/deactivate:
 *   put:
 *     summary: Deactivates an agent
 *     tags: [Agents]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Agent ID
 *     responses:
 *       200:
 *         description: Agent deactivated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AgentResponseDto'
 *       404:
 *         description: Agent not found
 *     security:
 *       - bearerAuth: []
 */
router.put('/:id/deactivate', agentController.deactivate);

export default router;
