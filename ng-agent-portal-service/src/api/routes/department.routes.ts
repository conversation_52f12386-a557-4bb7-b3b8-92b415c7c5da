import {Router} from 'express';
import departmentController from '@controllers/department.controller';
import {applyFilters} from '@middlewares/filter.middleware';
import Department from '@db/models/department.model';
import Agent from '@db/models/agent.model';
import {validateDto} from '@middlewares/validation.middleware';
import {CreateDepartmentDto, UpdateDepartmentDto} from '../dtos/department.dto';
import {authenticate} from '@middlewares/authenticate.middleware';

const router = Router();

// Apply authentication middleware to all routes
router.use(authenticate);

/**
 * @swagger
 * tags:
 *   name: Departments
 *   description: Department management
 */

// GET /departments
router.get(
  '/',
  applyFilters(Department, {
    allowedAttributes: ['id', 'name', 'isDefault', 'createdAt', 'updatedAt'],
    maxLimit: 100,
    allowedOperators: [], // All operators are allowed
    allowedIncludes: {
      Agent: {model: Agent, as: 'agents'},
    },
  }),
  departmentController.findAll,
);

// GET /departments/:id
router.get(
  '/:id',
  applyFilters(Department, {
    allowedAttributes: ['id', 'name', 'isDefault', 'createdAt', 'updatedAt'],
    allowedIncludes: {
      Agent: {model: Agent, as: 'agents'},
    },
  }),
  departmentController.findById,
);

/**
 * @swagger
 * /departments:
 *   post:
 *     summary: Create a new department
 *     tags: [Departments]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateDepartmentDto'
 *     responses:
 *       201:
 *         description: Department created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/DepartmentResponseDto'
 *       400:
 *         description: Validation error
 *     security:
 *       - bearerAuth: []
 */
router.post('/', validateDto(CreateDepartmentDto), departmentController.create);

/**
 * @swagger
 * /departments/{id}:
 *   put:
 *     summary: Update a department
 *     tags: [Departments]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Department ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateDepartmentDto'
 *     responses:
 *       200:
 *         description: Department updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/DepartmentResponseDto'
 *       404:
 *         description: Department not found
 *     security:
 *       - bearerAuth: []
 */
router.patch(
  '/:id',
  validateDto(UpdateDepartmentDto),
  departmentController.update,
);

/**
 * @swagger
 * /departments/{id}:
 *   delete:
 *     summary: Delete a department
 *     tags: [Departments]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Department ID
 *     responses:
 *       204:
 *         description: Department deleted successfully
 *       404:
 *         description: Department not found
 *     security:
 *       - bearerAuth: []
 */
router.delete('/:id', departmentController.delete);

export default router;
