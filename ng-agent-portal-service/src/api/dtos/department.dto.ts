import {Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eng<PERSON>} from 'class-validator';

/**
 * @swagger
 * components:
 *   schemas:
 *     CreateDepartmentDto:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         name:
 *           type: string
 *           maxLength: 100
 *           description: Department name
 *       example:
 *         name: Customer Support
 *
 *     UpdateDepartmentDto:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           maxLength: 100
 *           description: Department name
 *       example:
 *         name: Technical Support
 *
 *     DepartmentResponseDto:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         name:
 *           type: string
 *         isDefault:
 *           type: boolean
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *       example:
 *         id: 1
 *         name: Customer Support
 *         isDefault: false
 *         createdAt: "2024-06-01T12:00:00Z"
 *         updatedAt: "2024-06-01T12:00:00Z"
 */

export class CreateDepartmentDto {
  @IsString()
  @IsNotEmpty({message: 'Department name is required'})
  @MaxLength(100, {message: 'Department name cannot exceed 100 characters'})
  name!: string;
}

export class UpdateDepartmentDto {
  @IsString()
  @IsOptional()
  @IsNotEmpty({message: 'Department name cannot be empty'})
  @MaxLength(100, {message: 'Department name cannot exceed 100 characters'})
  name?: string;
}
