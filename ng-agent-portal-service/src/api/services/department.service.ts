import {Transaction, FindOptions} from 'sequelize';
import Department from '@db/models/department.model';
import {UpdateDepartmentDto} from '@dtos/department.dto';
import departmentRepository from '@db/repositories/department.repository';
import {BadRequestError, ConflictError, NotFoundError} from '@utils/errors';
import externalAgentService from './external-agent.service';
import {AgentUser} from '../../types';

const useRemote = process.env.USE_REMOTE_AGENT_API === 'true';

/**
 * Service for department operations
 */
class DepartmentService {
  /**
   * Create a new department
   * @param data - Department data
   * @param transaction - Optional transaction
   * @returns Promise with created department
   */
  async create(data: any, transaction?: Transaction): Promise<Department> {
    // Check if a department with the same name already exists
    const existingDepartment = await departmentRepository.findOne({
      where: {name: data.name},
      paranoid: false, // Check even soft-deleted records
      transaction,
    });

    if (existingDepartment) {
      throw new ConflictError('Department with this name already exists');
    }
    return departmentRepository.create(data, {transaction});
  }

  /**
   * Get all departments with filtering
   * @param options - Find options
   * @param token - Optional auth token for remote API calls
   * @returns Promise with departments array
   */
  async findAll(options?: FindOptions, token?: string): Promise<Department[]> {
    const needsRemoteAgents = this.shouldIncludeRemoteAgents(options);
    if (!needsRemoteAgents) {
      return departmentRepository.findAll(options);
    }

    const departmentOptions = this.removeAgentInclude(options);
    const departments = await departmentRepository.findAll(
      departmentOptions ? {...departmentOptions, raw: true} : {raw: true},
    );
    if (!token) {
      return departments;
    }

    return this.attachRemoteAgents(departments, token);
  }

  /**
   * Get department by ID
   * @param id - Department ID
   * @param options - Find options
   * @param token - Optional auth token for remote API calls
   * @returns Promise with department
   */
  async findById(
    id: string,
    options?: FindOptions,
    token?: string,
  ): Promise<Department> {
    // Check if we need to include agents from remote API
    const needsRemoteAgents =
      useRemote &&
      Array.isArray(options?.include) &&
      options?.include?.some(
        include =>
          typeof include === 'object' &&
          'model' in include &&
          'as' in include &&
          include.as === 'agents',
      );

    // If we don't need remote agents, just use the repository
    if (!needsRemoteAgents) {
      const department = await departmentRepository.findById(id, options);
      if (!department) {
        throw new NotFoundError('Department not found');
      }
      return department;
    }

    // Clone options and remove Agent include
    const departmentOptions = {...options};
    if (departmentOptions.include) {
      departmentOptions.include = (departmentOptions.include as any[]).filter(
        include =>
          !(
            typeof include === 'object' &&
            'model' in include &&
            'as' in include &&
            include.as === 'agents'
          ),
      );

      // If include is empty after filtering, remove it
      if (departmentOptions.include.length === 0) {
        delete departmentOptions.include;
      }
    }

    // Get department
    const department = await departmentRepository.findById(
      id,
      departmentOptions,
    );
    if (!department) {
      throw new NotFoundError('Department not found');
    }

    // If token is not provided, we can't fetch remote agents
    if (!token) {
      return department;
    }

    // Fetch agents for this department
    try {
      const agents = await externalAgentService.getAgentsByDepartmentId(
        department.id,
        token,
      );

      // Add agents to department
      (department as any).agents = agents;
    } catch (error) {
      console.error(
        `Error fetching agents for department ${department.id}:`,
        error,
      );
      // Continue with empty agents array if there's an error
      (department as any).agents = [];
    }

    return department;
  }

  /**
   * Update department
   * @param id - Department ID
   * @param data - Update data
   * @param transaction - Optional transaction
   * @returns Promise with updated department
   */
  async update(
    id: string,
    data: UpdateDepartmentDto,
    transaction?: Transaction,
  ): Promise<Department> {
    const department = await departmentRepository.findById(id, {transaction});

    if (!department) {
      throw new NotFoundError('Department not found');
    }

    // Check if name is being updated and if it already exists
    if (data.name && data.name !== department.name) {
      const existingDepartment = await departmentRepository.findOne({
        where: {name: data.name},
        paranoid: false, // Check even soft-deleted records
        transaction,
      });

      if (existingDepartment) {
        throw new ConflictError('Department with this name already exists');
      }
    }
    return departmentRepository.update(id, data, {transaction});
  }

  /**
   * Delete department
   * @param id - Department ID
   * @param transaction - Optional transaction
   * @returns Promise with void
   */
  async delete(id: string, transaction?: Transaction): Promise<void> {
    const department = await this.findById(id, {transaction});

    if (!department) {
      throw new NotFoundError('Department not found');
    }

    if (department.isDefault) {
      throw new BadRequestError('Cannot delete the default department');
    }
    return departmentRepository.delete(id, {transaction});
  }

  /**
   * Get default department
   * @returns Promise with default department or null
   */
  async getDefaultDepartment(): Promise<Department | null> {
    return departmentRepository.getDefaultDepartment();
  }

  /**
   * Count departments with filtering
   * @param options - Find options
   * @returns Promise with count
   */
  async count(options?: FindOptions): Promise<number> {
    return departmentRepository.count(options);
  }

  private shouldIncludeRemoteAgents(options?: FindOptions): boolean {
    return (
      useRemote &&
      Array.isArray(options?.include) &&
      options.include.some(
        include =>
          typeof include === 'object' &&
          'model' in include &&
          'as' in include &&
          include.as === 'agents',
      )
    );
  }
  private removeAgentInclude(options?: FindOptions): FindOptions {
    const clonedOptions = {...options};

    if (clonedOptions.include) {
      clonedOptions.include = (clonedOptions.include as any[]).filter(
        include =>
          !(
            typeof include === 'object' &&
            'model' in include &&
            'as' in include &&
            include.as === 'agents'
          ),
      );

      if (clonedOptions.include.length === 0) {
        delete clonedOptions.include;
      }
    }

    return clonedOptions;
  }

  private async attachRemoteAgents(
    departments: Department[],
    token: string,
  ): Promise<Department[]> {
    const agents = (await externalAgentService.getAllAgents(
      {roleName: 'systemgroup~agentuser3~Agent User'},
      token,
    )) as {data: AgentUser[]};

    return departments.map((department: any) => {
      const agentsForDepartment = agents?.data?.filter(
        agent => agent.dpmt_id === department.id,
      );
      department.agents = agentsForDepartment;
      return department;
    }) as any;
  }
}

export default new DepartmentService();
