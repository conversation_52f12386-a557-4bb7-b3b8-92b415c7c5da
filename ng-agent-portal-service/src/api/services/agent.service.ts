import Agent from '@db/models/agent.model';
// import {UpdateAgentDto} from '@dtos/agent.dto';
import {BadRequestError, ConflictError, NotFoundError} from '@utils/errors';
import {Transaction, FindOptions} from 'sequelize';
import agentRepository from '@db/repositories/agent.repository';
import departmentRepository from '@db/repositories/department.repository';
import {CreateAgentRequest} from '../../types/external-agent';
import externalAgentService from './external-agent.service';
const useRemote = process.env.USE_REMOTE_AGENT_API === 'true';

class AgentService {
  /**
   * Create a new agent
   * @param data - Agent data
   * @param transaction - Optional transaction
   * @returns Promise with created agent
   */
  async create({
    data,
    transaction,
    token,
  }: {
    data: any;
    transaction?: Transaction;
    token: any;
  }): Promise<Agent> {
    const {name, email, mobile, departmentName} = data;
    const department = await departmentRepository.findOne({
      where: {name: departmentName},
      transaction,
    });
    if (!department) {
      throw new BadRequestError('Invalid department name');
    }
    const departmentId = department.id;
    if (useRemote) {
      const payload: CreateAgentRequest = {
        name,
        email,
        countryCode: '91',
        contactNo: mobile,
        authType: 'a1',
        role: {
          id: process.env.AGENT_ROLE_ID!,
          name: process.env.AGENT_ROLE_NAME!,
          template: process.env.AGENT_ROLE_TEMPLATE!,
        },
        dpmt_id: departmentId,
        description: '',
      };

      const created = await externalAgentService.createAgent(payload, token);
      return created;
    } else {
      const dbData = {
        name,
        email,
        mobile,
        departmentId,
        availability: 'offline', // default value
        status: 'inactive', // default value
      };
      return agentRepository.create(dbData, {transaction});
    }
  }

  /**
   * Get all agents with filters optional
   * @returns Promise with agent array
   */
  async findAll({
    options,
    token,
    params,
  }: {
    options?: FindOptions;
    token: any;
    params?: any;
  }): Promise<Agent[]> {
    if (useRemote) {
      const agents = await externalAgentService.getAllAgents(params, token);
      return agents;
    } else {
      return agentRepository.findAll(options);
    }
  }

  /**
   * Get agent by ID
   * @param id - Agent ID
   * @returns Promise with Agent
   */
  async findById(id: string, token: any): Promise<Agent> {
    if (useRemote) {
      const agent = await externalAgentService.getById(id, token);
      if (!agent) {
        throw new NotFoundError('agent not found');
      }

      return agent;
    } else {
      const agent = await agentRepository.findById(id);
      if (!agent) {
        throw new NotFoundError('Agent not found');
      }
      return agent;
    }
  }

  /**
   * Update Agent
   * @param id - Agent ID
   * @param data - Update data
   * @param transaction - Optional transaction
   * @returns Promise with updated agent
   */
  async update({
    id,
    data,
    transaction,
    token,
  }: {
    id: string;
    data: any;
    transaction?: Transaction;
    token: any;
  }): Promise<Agent> {
    if (useRemote) {
      const agent = externalAgentService.updateAgent(id, data, token);
      return agent;
    } else {
      const agent = await agentRepository.findById(id, {transaction});
      if (!agent) {
        throw new NotFoundError('Agent not found');
      }

      // Check if email is being updated and if it already exists
      if (data.email && data.email !== agent.email) {
        const existingAgent = await agentRepository.findOne({
          where: {name: data.name},
          paranoid: false, // Check even soft-deleted records
          transaction,
        });

        if (existingAgent) {
          throw new ConflictError('Agent with this email already exists');
        }
      }
      return agentRepository.update(id, data, {transaction});
    }
  }

  /**
   * Delete agent
   * @param id - Agent ID
   * @param transaction - Optional transaction
   * @returns Promise with void
   */
  async delete({
    id,
    transaction,
    token,
  }: {
    id: string;
    transaction?: Transaction;
    token: any;
  }): Promise<void> {
    if (useRemote) {
      const agent = await externalAgentService.deleteAgent(id, token);
      return agent;
    } else {
      const agent = await agentRepository.findById(id, {transaction});
      if (!agent) {
        throw new NotFoundError('Agent not found');
      }
      return agentRepository.delete(id, {transaction});
    }
  }

  /**
   * Activate agent
   * @param id - Agent ID
   * @param transaction - Optional transaction
   * @returns Promise with void
   */
  async activate({
    id,
    transaction,
    token,
  }: {
    id: string;
    transaction?: Transaction;
    token: any;
  }): Promise<Agent> {
    if (useRemote) {
      const agent = await externalAgentService.activateAgent(id, token);
      return agent;
    } else {
      const agent = await agentRepository.findById(id, {transaction});
      if (!agent) {
        throw new NotFoundError('Agent not found');
      }
      const data = {status: 'active'};
      return agentRepository.update(id, data, {transaction});
    }
  }

  /**
   * Deactivate agent
   * @param id - Agent ID
   * @param transaction - Optional transaction
   * @returns Promise with void
   */
  async deactivate({
    id,
    transaction,
    token,
  }: {
    id: string;
    transaction?: Transaction;
    token: any;
  }): Promise<Agent> {
    if (useRemote) {
      const agent = await externalAgentService.deactivateAgent(id, token);
      return agent;
    } else {
      const agent = await agentRepository.findById(id, {transaction});
      if (!agent) {
        throw new NotFoundError('Agent not found');
      }
      const data = {status: 'inactive'};
      return agentRepository.update(id, data, {transaction});
    }
  }

  /**
   * Count agents with filtering
   * @param options - Find options
   * @returns Promise with count
   */
  async count(options?: FindOptions): Promise<number> {
    return agentRepository.count(options);
  }
}

export default new AgentService();
