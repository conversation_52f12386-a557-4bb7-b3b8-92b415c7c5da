import axios from 'axios';
import {
  CreateAgentRequest,
  UpdateAgentRequest,
} from '../../types/external-agent';
import {ConflictError, HttpError} from '@utils/errors';
import {FindOptions} from 'sequelize';

class ExternalAgentService {
  private baseUrl = process.env.EXTERNAL_API_URL;

  /**
   * Create a new agent in Keycloak
   * @param data - Agent creation payload
   * @returns Created agent response from Keycloak
   */
  async createAgent(data: CreateAgentRequest, token: string): Promise<any> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/accounts/users`,
        data,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        },
      );
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 409) {
        throw new ConflictError(
          'Agent with this email/contact already exists in Keycloak',
        );
      }
      throw new HttpError(
        error.response?.status || 500,
        error.response?.data?.message || 'Failed to create agent in Keycloak',
      );
    }
  }

  async getAllAgents(
    options: FindOptions | Record<string, string>,
    token: string,
  ): Promise<any> {
    try {
      const response = await axios.get(`${this.baseUrl}/accounts/users`, {
        params: options,
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });
      return response.data;
    } catch (error: any) {
      throw new HttpError(
        error.response?.status || 500,
        error.response?.data?.message || 'Failed to get agents',
      );
    }
  }

  async getById(id: string, token: string): Promise<any> {
    try {
      const response = await axios.get(`${this.baseUrl}/accounts/users`, {
        params: {id},
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });
      const data = {
        name: response.data.name,
        email: response.data.email,
        mobile: `${response.data.countryCode}${response.data.contactNo}`,
        departmentName: response.data.dpmt_id,
      };
      return data;
    } catch (error: any) {
      throw new HttpError(
        error.response?.status || 500,
        error.response?.data?.message || 'Failed to get agents',
      );
    }
  }

  async updateAgent(
    id: string,
    data: UpdateAgentRequest,
    token: string,
  ): Promise<any> {
    try {
      const response = await axios.put(
        `${this.baseUrl}/accounts/users/${id}`,
        data,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        },
      );
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 409) {
        throw new ConflictError(
          'Agent with this email/contact already exists in Keycloak',
        );
      }
      throw new HttpError(
        error.response?.status || 500,
        error.response?.data?.message || 'Failed to update agent in Keycloak',
      );
    }
  }

  async activateAgent(id: string, token: string): Promise<any> {
    try {
      const response = await axios.put(
        `${this.baseUrl}/accounts/users/${id}/activate`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        },
      );
      return response.data;
    } catch (error: any) {
      throw new HttpError(
        error.response?.status || 500,
        error.response?.data?.message || 'Failed to update agent status',
      );
    }
  }

  async deactivateAgent(id: string, token: string): Promise<any> {
    try {
      const response = await axios.put(
        `${this.baseUrl}/accounts/users/${id}/deactivate`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        },
      );
      return response.data;
    } catch (error: any) {
      throw new HttpError(
        error.response?.status || 500,
        error.response?.data?.message || 'Failed to update agent status',
      );
    }
  }

  async deleteAgent(id: string, token: string): Promise<void> {
    try {
      const response = await axios.delete(
        `${this.baseUrl}/accounts/users/${id}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        },
      );
      return response.data;
    } catch (error: any) {
      throw new HttpError(
        error.response?.status || 500,
        error.response?.data?.message || 'Failed to delete agent',
      );
    }
  }

  async getAgentsByDepartmentId(
    departmentId: string,
    token: string,
  ): Promise<any> {
    try {
      const response = await axios.get(
        `${this.baseUrl}/accounts/users?dpmt_id=${departmentId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        },
      );
      return response.data;
    } catch (error: any) {
      throw new HttpError(
        error.response?.status ?? 500,
        error.response?.data?.message ?? 'Failed to get agents',
      );
    }
  }
}

export default new ExternalAgentService();
