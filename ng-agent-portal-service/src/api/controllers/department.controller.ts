import {Request, Response, NextFunction} from 'express';
import departmentService from '@services/department.service';
import {successResponse} from '@utils/response';

/**
 * Controller for department operations
 */
class DepartmentController {
  /**
   * Create a new department
   */
  async create(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const department = await departmentService.create(req.body);
      successResponse(res, department, 'Department created successfully', 201);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all departments
   */
  async findAll(
    req: Request,
    res: Response,
    next: NextFunction,
  ): Promise<void> {
    try {
      const options = req.filterOptions || {};
      const token = req.token ?? ''; // Get token from request
      const [departments, total] = await Promise.all([
        departmentService.findAll(options, token),
        departmentService.count(options),
      ]);

      successResponse(
        res,
        departments,
        'Departments retrieved successfully',
        200,
        {
          total,
          limit: options.limit,
          offset: options.offset || 0,
        },
      );
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get department by ID
   */
  async findById(
    req: Request,
    res: Response,
    next: NextFunction,
  ): Promise<void> {
    try {
      const options = req.filterOptions || {};
      const department = await departmentService.findById(
        req.params.id,
        options,
      );
      successResponse(res, department, 'Department retrieved successfully');
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update department
   */
  async update(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const department = await departmentService.update(
        req.params.id,
        req.body,
      );
      successResponse(res, department, 'Department updated successfully');
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete department
   */
  async delete(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      await departmentService.delete(req.params.id);
      res.status(204).send();
    } catch (error) {
      next(error);
    }
  }
}

export default new DepartmentController();
