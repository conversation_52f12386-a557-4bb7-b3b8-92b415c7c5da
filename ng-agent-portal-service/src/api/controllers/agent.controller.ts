import {Request, Response, NextFunction} from 'express';
import agentService from '@services/agent.service';
import {successResponse} from '@utils/response';

/**
 * Controller for agent operations
 */
class AgentController {
  /**
   * Create a new agent
   */
  async create(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const token = req.token; // Get token from request
      const agent = await agentService.create({data: req.body, token});
      successResponse(res, agent, 'Agent created successfully', 201);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get agent by ID
   */
  async findById(
    req: Request,
    res: Response,
    next: NextFunction,
  ): Promise<void> {
    try {
      const token = req.token; // Get token from request
      const agent = await agentService.findById(req.params.id, token);
      successResponse(res, agent, 'Agent retrieved successfully');
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all agents
   */
  async findAll(
    req: Request,
    res: Response,
    next: NextFunction,
  ): Promise<void> {
    try {
      const options = req.filterOptions || {};
      console.log('Filter options:', options);
      const params = req.query;
      console.log('Query params:', params);
      const token = req.token; // Get token from request
      const [agents] = await Promise.all([
        agentService.findAll({params, token}),
        // agentService.count(),
      ]);
      successResponse(res, agents, 'Agents retrieved successfully', 200);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update agent
   */
  async update(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const token = req.token; // Get token from request
      const agent = await agentService.update({
        id: req.params.id,
        data: req.body,
        token,
      });
      successResponse(res, agent, 'Agent updated successfully');
    } catch (error) {
      next(error);
    }
  }

  /**
   * Activate agent
   */
  async activate(
    req: Request,
    res: Response,
    next: NextFunction,
  ): Promise<void> {
    try {
      const token = req.token; // Get token from request
      const agent = await agentService.activate({id: req.params.id, token});
      successResponse(res, agent, 'Agent activated successfully');
    } catch (error) {
      next(error);
    }
  }

  /**
   * Dectivate agent
   */
  async deactivate(
    req: Request,
    res: Response,
    next: NextFunction,
  ): Promise<void> {
    try {
      const token = req.token; // Get token from request
      const agent = await agentService.deactivate({id: req.params.id, token});
      successResponse(res, agent, 'Agent deactivated successfully');
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete department
   */
  async delete(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const token = req.token; // Get token from request
      await agentService.delete({id: req.params.id, token});
      res.status(204).send();
    } catch (error) {
      next(error);
    }
  }
}

export default new AgentController();
