// middleware/parseQueryFilter.ts
import {Request, Response, NextFunction} from 'express';

export function parseQueryFilter(
  req: Request,
  res: Response,
  next: NextFunction,
) {
  if (req.query.filter && typeof req.query.filter === 'string') {
    try {
      const parsed = JSON.parse(req.query.filter);
      Object.assign(req.query, parsed);
      delete req.query.filter; // Optional: remove the original string
    } catch (e) {
      console.warn('Invalid filter JSON:', req.query.filter);
    }
  }
  next();
}
