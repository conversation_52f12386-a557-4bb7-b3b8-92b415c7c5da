import {Request, Response, NextFunction} from 'express';
import {AppError} from '@utils/errors';
import logger from '@utils/logger';
import {errorResponse} from '@utils/response';

export const errorHandler = (
  err: Error,
  req: Request,
  res: Response,
  _next: NextFunction,
): void => {
  // Set default error values
  let statusCode = 500;
  let message = 'Internal Server Error';
  let errors: Record<string, string[]> | undefined;

  // Handle known errors
  if (err instanceof AppError) {
    statusCode = err.statusCode;
    message = err.message;

    if ('errors' in err && err.errors) {
      errors = err.errors as Record<string, string[]>;
    }
  }

  // Log the error
  logger.error(`[${req.id ?? 'UNKNOWN'}] Error: ${message}`, {
    path: `${req.method} ${req.path}`,
    error: err.stack,
    statusCode,
  });

  // Send error response
  errorResponse(res, message, statusCode, errors);
};
