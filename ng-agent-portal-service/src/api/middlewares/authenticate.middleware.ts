import {Request, Response, NextFunction} from 'express';
import {UnauthorizedError} from '@utils/errors';
import logger from '@utils/logger';

// Extend Express Request interface to include agent
declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request {
      user?: any;
      token?: string;
    }
  }
}

export const authenticate = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    // Get token from Authorization header
    const authHeader = req.headers.authorization;
    // if (!authHeader?.startsWith('Bearer ')) {
    //   throw new UnauthorizedError('No token provided');
    // }

    // const token = authHeader.split(' ')[1];
    // if (!token) {
    //   throw new UnauthorizedError('No token provided');
    // }
    // Need to implement token verification
    //   if (!authHeader || !authHeader.startsWith('Bearer ')) {
    //   return res.status(401).json({ error: 'Missing or invalid Authorization header' });
    // }

    const token = authHeader?.split(' ')[1];

    // Attach token to request so downstream controllers/services can use it
    req.token = token;

    try {
      next();
    } catch (error) {
      logger.error('Token verification failed:', error);
      throw new UnauthorizedError('Invalid token');
    }
  } catch (error) {
    next(error);
  }
};
