export interface CreateAgentRequest {
  name: string;
  email: string;
  countryCode: string;
  contactNo: string;
  authType: string;
  role: {
    id: string;
    name: string;
    template: string;
  };
  description?: string;
  dpmt_id: string;
}

export interface UpdateAgentRequest {
  name: string;
  email: string;
  countryCode: string;
  contactNo: string;
  dpmt_id: string;
}

export interface Role {
  id: string;
  name: string;
  composite: boolean;
  clientRole: boolean;
  containerId: string;
}

export interface AgentUser {
  id: string;
  name: string;
  email: string;
  role: Role;
  createdDate: string; // consider `Date` if you parse it
  groupId: string;
  status: string;
  countryCode: string;
  contactNo: string;
  authType: string;
  expiryDate: string | null;
  creditLimit: number | null;
  description: string;
  filterscope: string;
  createdby: string;
  dpmt_id: string;
  phoneNumber: string;
  billingType: string;
}
