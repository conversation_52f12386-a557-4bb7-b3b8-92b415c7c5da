# nGage Agent Portal Service

A microservice for agent portal functionality for the nGage platform.

## Features

- Agent authentication and authorization
- Customer management
- Ticket management
- Interaction tracking
- Dashboard analytics

## Tech Stack

- Node.js with Express
- TypeScript
- Sequelize ORM with PostgreSQL
- JWT for authentication
- Swagger API documentation
- Class-validator for DTO validation
- <PERSON> for logging

## Getting Started

### Prerequisites

- Node.js 16+
- PostgreSQL 12+

### Installation

1. Clone the repository
2. Install dependencies:

```bash
npm install
```

3. Create a `.env` file based on `.env.example`:

```bash
cp .env.example .env
```

4. Update the environment variables in the `.env` file

5. Run database migrations:

```bash
npm run migrate
```

6. Start the development server:

```bash
npm run dev
```

The API will be available at http://localhost:3003/api/v1 and the Swagger documentation at http://localhost:3003/api-docs.

## API Endpoints

### Authentication

- `POST /api/v1/auth/login` - Login as an agent
- `POST /api/v1/auth/refresh-token` - Refresh access token
- `POST /api/v1/auth/logout` - Logout
- `GET /api/v1/auth/me` - Get current agent
- `POST /api/v1/auth/change-password` - Change password

### Agents

- `GET /api/v1/agents` - Get all agents
- `GET /api/v1/agents/:id` - Get an agent by ID
- `POST /api/v1/agents` - Create a new agent
- `PUT /api/v1/agents/:id` - Update an agent
- `DELETE /api/v1/agents/:id` - Delete an agent
- `GET /api/v1/agents/:id/tickets` - Get tickets assigned to an agent

### Customers

- `GET /api/v1/customers` - Get all customers
- `GET /api/v1/customers/:id` - Get a customer by ID
- `POST /api/v1/customers` - Create a new customer
- `PUT /api/v1/customers/:id` - Update a customer
- `GET /api/v1/customers/:id/tickets` - Get tickets for a customer

### Tickets

- `GET /api/v1/tickets` - Get all tickets
- `GET /api/v1/tickets/:id` - Get a ticket by ID
- `POST /api/v1/tickets` - Create a new ticket
- `PUT /api/v1/tickets/:id` - Update a ticket
- `POST /api/v1/tickets/:id/assign` - Assign a ticket to an agent
- `POST /api/v1/tickets/:id/status` - Update ticket status
- `GET /api/v1/tickets/:id/interactions` - Get interactions for a ticket

### Interactions

- `GET /api/v1/interactions` - Get all interactions
- `GET /api/v1/interactions/:id` - Get an interaction by ID
- `POST /api/v1/interactions` - Create a new interaction

### Dashboard

- `GET /api/v1/dashboard/summary` - Get dashboard summary
- `GET /api/v1/dashboard/tickets-by-status` - Get tickets by status
- `GET /api/v1/dashboard/tickets-by-category` - Get tickets by category
- `GET /api/v1/dashboard/tickets-over-time` - Get tickets over time
- `GET /api/v1/dashboard/agent-performance` - Get agent performance

## Database Schema

The service uses the following database models:

- **Agent**: Represents an agent with authentication details
- **Customer**: Represents a customer
- **Ticket**: Represents a support ticket
- **Interaction**: Represents an interaction on a ticket

## Development

### Available Scripts

- `npm run dev` - Start the development server with hot-reload
- `npm run build` - Build the application
- `npm start` - Start the production server
- `npm run migrate` - Run database migrations
- `npm run migrate:undo` - Revert the last migration
- `npm run seed` - Run database seeders
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run format` - Format code with Prettier
- `npm test` - Run tests

## Docker

Build the Docker image:

```bash
docker build -t ng-agent-portal-service .
```

Run the container:

```bash
docker run -p 3003:3003 --env-file .env ng-agent-portal-service
```

## License

[MIT](LICENSE)
